# Podman 容器化部署指南

## 📋 概述

本文檔說明如何使用 Podman 容器化部署 Outlook Summary 系統，統一在 **port 5000** 提供服務。

## 🛠 系統需求

### 必要軟體
- **Podman**: 4.0+ (作為 Docker 替代方案)
- **podman-compose**: 用於多容器編排
- **PowerShell** (Windows) 或 **Bash** (Linux/Mac)

### 安裝 Podman

#### Windows
```powershell
# 使用 Chocolatey
choco install podman-desktop

# 或下載官方安裝包
# https://podman.io/getting-started/installation#windows
```

#### Linux (Ubuntu/Debian)
```bash
sudo apt-get update
sudo apt-get install podman
```

#### macOS
```bash
brew install podman
```

### 安裝 podman-compose
```bash
pip install podman-compose
```

## 📁 容器化架構

### 服務組件
- **outlook_app**: 主應用容器 (Flask + FastAPI + Dramatiq Worker)
- **redis**: Redis 任務佇列容器
- **數據持久化**: 掛載卷保存數據、日誌、上傳文件

### 網絡配置
- **統一端口**: Port 5000
- **內部通信**: 容器間網絡
- **健康檢查**: 自動監控服務狀態

## 🚀 快速部署

### 1. 準備環境變數

確保存在 `.env` 文件：

```bash
# 郵件配置 (必要)
EMAIL_ADDRESS=<EMAIL>
EMAIL_PASSWORD=your-app-password
POP3_SERVER=pop.gmail.com
POP3_PORT=995

# LINE 通知配置 (可選)
LINE_CHANNEL_ACCESS_TOKEN=your-line-token
LINE_USER_ID=your-line-user-id
LINE_NOTIFY_PARSING_FAILURE=true
LINE_NOTIFY_PARSING_SUCCESS=false

# 系統配置
FLASK_ENV=production
USE_MEMORY_BROKER=false
```

### 2. Windows 用戶 - 使用 PowerShell

```powershell
# 一鍵啟動（構建 + 運行）
.\start_podman.ps1 -Mode up

# 背景運行
.\start_podman.ps1 -Mode up -Background

# 強制重建
.\start_podman.ps1 -Mode rebuild -Force

# 查看日誌
.\start_podman.ps1 -Mode logs

# 查看狀態
.\start_podman.ps1 -Mode status

# 停止服務
.\start_podman.ps1 -Mode down
```

### 3. Linux/Mac 用戶 - 使用 Bash

```bash
# 一鍵啟動（構建 + 運行）
./start_podman.sh up

# 背景運行
./start_podman.sh up true

# 強制重建
./start_podman.sh rebuild false true

# 查看日誌
./start_podman.sh logs

# 查看狀態
./start_podman.sh status

# 停止服務
./start_podman.sh down

# 顯示幫助
./start_podman.sh help
```

### 4. 手動部署

```bash
# 1. 構建容器鏡像
podman build -t outlook-summary -f Containerfile .

# 2. 啟動所有服務
podman-compose -f podman-compose.yml up -d

# 3. 查看運行狀態
podman-compose -f podman-compose.yml ps

# 4. 停止服務
podman-compose -f podman-compose.yml down
```

## 🌐 訪問服務

部署成功後，您可以通過以下地址訪問：

- **主要服務**: http://localhost:5000
- **健康檢查**: http://localhost:5000/health
- **PTS Renamer**: http://localhost:5000/pts-renamer/
- **系統監控**: http://localhost:5000/monitoring/

## 🔧 配置說明

### 容器配置 (Containerfile)

```dockerfile
# 基礎鏡像: Python 3.11.9
FROM python:3.11.9-slim

# 工作目錄: /app
WORKDIR /app

# 環境變量
ENV FLASK_PORT=5000
ENV FASTAPI_PORT=5001
ENV USE_MEMORY_BROKER=false

# 暴露端口: 5000
EXPOSE 5000
```

### 服務編排 (podman-compose.yml)

```yaml
services:
  redis:
    image: docker.io/redis:7-alpine
    ports:
      - "6379:6379"
    
  outlook_app:
    build: .
    ports:
      - "5000:5000"
    depends_on:
      - redis
    environment:
      - FLASK_PORT=5000
      - FASTAPI_PORT=5001
```

## 📊 監控與維護

### 查看容器狀態
```bash
# 查看運行中的容器
podman ps

# 查看所有容器（包括停止的）
podman ps -a

# 查看容器日誌
podman logs outlook_main
podman logs outlook_redis
```

### 健康檢查
```bash
# 檢查應用健康狀態
curl http://localhost:5000/health

# 檢查 Redis 狀態
podman exec outlook_redis redis-cli ping
```

### 性能監控
```bash
# 查看容器資源使用
podman stats

# 查看系統資源
podman system df
```

## 🐛 故障排除

### 常見問題

#### 1. 端口衝突
```bash
# 檢查端口占用
netstat -an | grep :5000

# 或使用 ss 命令
ss -tulpn | grep :5000
```

#### 2. 容器無法啟動
```bash
# 查看詳細錯誤日誌
podman logs outlook_main

# 檢查鏡像是否存在
podman images outlook-summary
```

#### 3. 郵件連接失敗
- 檢查 `.env` 文件中的郵件配置
- 確認郵件服務器設置正確
- 檢查網絡連接

#### 4. Redis 連接問題
```bash
# 檢查 Redis 容器狀態
podman exec outlook_redis redis-cli ping

# 重啟 Redis 容器
podman restart outlook_redis
```

### 清理與重置

```bash
# 停止並刪除所有容器
podman-compose -f podman-compose.yml down

# 刪除所有相關鏡像
podman rmi outlook-summary

# 清理未使用的資源
podman system prune -a

# 重建完整系統
./start_podman.sh rebuild
```

## 📈 生產環境優化

### 1. 資源限制
在 `podman-compose.yml` 中添加資源限制：

```yaml
services:
  outlook_app:
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: "1.5"
        reservations:
          memory: 512M
          cpus: "0.5"
```

### 2. 日誌輪轉
```yaml
services:
  outlook_app:
    logging:
      driver: json-file
      options:
        max-size: "100m"
        max-file: "3"
```

### 3. 備份策略
```bash
# 備份數據卷
podman volume export outlook_summary_app_data > app_data_backup.tar

# 恢復數據卷
podman volume import outlook_summary_app_data app_data_backup.tar
```

## 🔒 安全考慮

### 1. 容器安全
- 使用非 root 用戶運行容器
- 定期更新基礎鏡像
- 掃描容器漏洞

### 2. 網絡安全
- 使用內部網絡通信
- 限制對外暴露的端口
- 配置防火牆規則

### 3. 數據安全
- 加密敏感環境變量
- 定期備份數據
- 配置訪問控制

## 🎯 最佳實踐

1. **監控**: 使用健康檢查和日誌監控
2. **備份**: 定期備份重要數據和配置
3. **更新**: 定期更新依賴和基礎鏡像
4. **測試**: 在生產環境部署前充分測試
5. **文檔**: 維護部署和配置文檔

## 📞 技術支持

如遇到問題，請：

1. 檢查本文檔的故障排除部分
2. 查看容器日誌: `podman logs outlook_main`
3. 檢查系統狀態: `./start_podman.sh status`
4. 參考專案的 GitHub Issues

---

*🐧 本部署方案基於 Podman 容器化技術，為 Outlook Summary 系統提供可靠、可擴展的生產級部署解決方案。*