# PTS Renamer 核心功能位置分析 - 2025-08-23

## 📊 分析概要
**分析日期**: 2025-08-23
**分析目的**: 明確定位 PTS 重新命名和 QC 生成功能的實作位置
**分析範圍**: 後端服務層和 Dramatiq Worker 執行環境

---

## 🔄 **1. PTS 重新命名功能**

### **核心服務提供者**
- **檔案位置**: `backend/pts_renamer/services/pts_rename_processor.py`
- **類別名稱**: `PTSRenameProcessor`
- **實例化位置**: `PTSRenameServiceFactory.create_processor()`

### **主要方法實作**

#### **rename_file 方法** (第 37-71 行)
```python
def rename_file(self, file_path: Path, old_pattern: str, new_pattern: str) -> Tuple[bool, str, str]:
    """
    執行單個 PTS 檔案重命名
    
    處理流程:
    1. 生成新檔名 (調用 generate_new_name)
    2. 檢查模式是否匹配
    3. 檢查目標檔案是否已存在
    4. 執行實際重命名操作
    5. 記錄操作結果
    
    返回: (成功標誌, 原檔名, 新檔名或錯誤訊息)
    """
```

#### **generate_new_name 方法** (第 73-116 行)
```python
def generate_new_name(self, file_path: Path, old_pattern: str, new_pattern: str) -> str:
    """
    根據模式生成新檔名
    
    支援的替換模式:
    - {num}: 檔名中的數字
    - {old}: 原始檔名
    - {ext}: 副檔名
    - 正規表達式模式匹配
    """
```

#### **batch_rename_files 方法** (第 197-223 行)
```python
def batch_rename_files(self, folder_paths: List[Path], old_pattern: str, new_pattern: str) -> List[PTSRenameResult]:
    """
    批量處理多個 PTS 檔案
    支援多資料夾掃描和批次重命名
    """
```

### **重命名邏輯特點**
1. **模式匹配**: 支援簡單替換和正規表達式
2. **衝突檢測**: 自動檢查目標檔案是否存在
3. **錯誤處理**: 完整的異常捕獲和錯誤回報
4. **批量支援**: 可同時處理多個檔案和資料夾

---

## 📋 **2. QC 檔案生成功能**

### **核心服務提供者**
- **檔案位置**: `backend/pts_renamer/services/pts_rename_qc_generator.py`
- **類別名稱**: `PTSQCGenerator`
- **實例化位置**: `PTSRenameServiceFactory.create_qc_generator()`

### **主要方法實作**

#### **create_qc_file 方法** (第 40-103 行)
```python
def create_qc_file(self, pts_file_path: Path) -> Tuple[bool, str]:
    """
    從 PTS 檔案創建 QC 檔案
    
    處理邏輯:
    1. 檢查是否已經是 QC 檔案 (避免重複處理)
    2. 讀取原始 PTS 檔案內容
    3. 找到 "Parameter," 和 "QA," 行位置
    4. 保留 Parameter 行之前的內容
    5. 跳過 Parameter 到 QA 之間的內容
    6. 保留 QA 行之後的內容
    7. 生成檔名: 原檔名_QC.pts
    8. 寫入新的 QC 檔案
    9. 應用額外的 QC 修改 (modify_qc_content)
    
    返回: (成功標誌, QC檔名或錯誤訊息)
    """
```

#### **modify_qc_content 方法** (第 105-201 行)
```python
def modify_qc_content(self, qc_file_path: Path) -> None:
    """
    修改 QC 檔案內容
    
    修改邏輯:
    1. 在 HSMS 行後插入特定欄位
    2. 處理 DATA 區塊的數值
    3. 根據規則調整測試參數
    """
```

#### **batch_generate_qc_files 方法** (第 247-271 行)
```python
def batch_generate_qc_files(self, folder_paths: List[Path]) -> List[Tuple[bool, str]]:
    """
    批量生成 QC 檔案
    掃描資料夾中的所有 PTS 檔案並生成對應 QC 檔案
    """
```

### **QC 生成特點**
1. **內容過濾**: 移除 Parameter 和 QA 之間的測試數據
2. **檔名規則**: 自動添加 "_QC" 後綴
3. **重複檢查**: 避免對已經是 QC 的檔案重複處理
4. **內容修改**: 支援自定義 QC 內容調整規則

---

## 🏭 **3. 服務整合架構**

### **服務工廠 (Service Factory)**
- **檔案**: `backend/pts_renamer/services/pts_rename_service_factory.py`
- **類別**: `PTSRenameServiceFactory`

### **依賴注入流程**
```python
class PTSRenameServiceFactory:
    def create_service(self) -> PTSRenameService:
        # 創建處理器實例
        processor = self.create_processor()      # PTSRenameProcessor
        qc_generator = self.create_qc_generator() # PTSQCGenerator
        
        # 注入到主服務
        service = PTSRenameService(
            repository=repository,
            processor=processor,
            qc_generator=qc_generator,
            directory_manager=directory_manager,
            download_service=download_service,
            task_queue=task_queue,
            logger=logger
        )
        return service
```

### **服務調用鏈**
```
Flask Route Handler
    ↓
PTSRenamePresenter (MVP Presenter)
    ↓
PTSRenameService (Core Service)
    ↓
Dramatiq Task Queue
    ↓
Dramatiq Worker Process
    ↓
PTSRenameProcessor + PTSQCGenerator (實際執行)
```

---

## ⚙️ **4. Dramatiq Worker 執行環境**

### **任務定義位置**
- **檔案**: `backend/pts_renamer/services/pts_rename_dramatiq_integration.py`
- **函數**: `process_pts_rename_job_task`

### **執行流程**
```python
@dramatiq.actor(queue_name="pts_rename_queue")
async def process_pts_rename_job_task(job_id: str, job_data: Dict):
    # 初始化服務
    services = _initialize_pts_services()
    processor = services['processor']        # PTSRenameProcessor 實例
    qc_generator = services['qc_generator']  # PTSQCGenerator 實例
    
    # 處理每個 PTS 檔案
    for pts_file in job.pts_files:
        # 執行重命名
        if PTSOperationType.RENAME in job.operations:
            success, old_name, new_name = await processor.rename_file(
                file_path,
                job.rename_pattern.old_pattern,
                job.rename_pattern.new_pattern
            )
        
        # 生成 QC 檔案
        if job.qc_enabled:
            success, qc_filename = await qc_generator.create_qc_file(file_path)
```

### **Worker 執行特性**
1. **異步執行**: 使用 async/await 進行非阻塞處理
2. **進度追蹤**: 實時更新處理進度 (0-100%)
3. **錯誤重試**: 最多重試 3 次
4. **狀態持久化**: 透過資料庫記錄處理狀態

---

## 📍 **5. 關鍵發現總結**

### **功能定位**
| 功能 | 提供類別 | 檔案位置 | 執行環境 |
|------|---------|---------|----------|
| **PTS 重命名** | PTSRenameProcessor | pts_rename_processor.py | Dramatiq Worker |
| **QC 生成** | PTSQCGenerator | pts_rename_qc_generator.py | Dramatiq Worker |
| **服務協調** | PTSRenameService | pts_rename_service.py | Flask Process |
| **任務排程** | Dramatiq Integration | pts_rename_dramatiq_integration.py | Redis + Dramatiq |

### **資料流向**
```
使用者點擊 "開始處理"
    ↓
Flask API 接收請求 (/pts-renamer/api/process)
    ↓
PTSRenamePresenter 驗證請求
    ↓
PTSRenameService 創建任務
    ↓
Dramatiq 排程任務到 Redis
    ↓
Dramatiq Worker 提取任務
    ↓
PTSRenameProcessor 執行重命名
    ↓
PTSQCGenerator 生成 QC 檔案
    ↓
結果存儲和狀態更新
```

### **技術要點**
1. **模組化設計**: 功能明確分離，便於維護
2. **依賴注入**: 使用工廠模式管理服務依賴
3. **異步處理**: 背景任務不阻塞主程序
4. **錯誤恢復**: 完善的重試和錯誤處理機制
5. **進度追蹤**: 實時反饋處理進度給前端

---

## 🔍 **6. 當前問題診斷**

### **已識別問題**
1. **PTS 檔案未儲存到資料庫**: 上傳時檔案未正確儲存到 pts_rename_files 表
2. **Upload ID 不一致**: 上傳和處理使用不同的 ID 格式
3. **Flask 路由未被觸發**: 上傳請求可能被其他服務攔截

### **解決方向**
1. 修復 `pts_rename_upload_service.py` 中的檔案儲存邏輯
2. 確保上傳時正確提取和儲存 PTS 檔案到資料庫
3. 追查實際處理上傳請求的服務端點

這份分析完整記錄了 PTS Renamer 核心功能的實作位置和執行機制，為後續的問題修復和功能擴展提供了準確的技術參考。