"""
PTS Renamer Presenter

This module contains the presenter layer for the PTS Renamer MVP architecture.
The presenter acts as the intermediary between the view (web interface) and 
the model (business logic), handling user interactions and coordinating 
business operations.
"""

from typing import List, Dict, Any, Optional, Tuple
from pathlib import Path
# import logging  # Using loguru instead
from datetime import datetime, timedelta

from ..models.pts_rename_models import (
    PTSRenameJobRequest,
    PTSRenameJobStatus,
    PTSRenamePreviewRequest,
    PTSRenamePreviewResponse,
    PTSRenameUploadResponse,
    PTSRenameErrorResponse,
    PTSErrorCodes
)
from ..services.pts_rename_service import PTSRenameService, ServiceError
from ..services.pts_rename_upload_service import PTSRenameUploadService
# Using loguru for logging


class PTSRenamePresenter:
    """
    Presenter layer for PTS Renamer MVP architecture
    
    This class handles all user interactions and coordinates between
    the view layer (Flask routes/FastAPI endpoints) and the business
    logic layer (services). It ensures proper error handling, logging,
    and response formatting.
    """
    
    def __init__(self,
                 pts_service: PTSRenameService,
                 upload_service: PTSRenameUploadService,
                 logger = None):
        """
        Initialize PTS Rename Presenter
        
        Args:
            pts_service: Core PTS processing service
            upload_service: File upload handling service
            logger: Optional logger instance
        """
        self.pts_service = pts_service
        self.upload_service = upload_service
        from loguru import logger as loguru_logger
        self.logger = logger or loguru_logger
    
    async def handle_upload_request(self, files: List[Any]) -> Dict[str, Any]:
        """
        Handle file upload request from web interface
        
        This method processes uploaded files, validates them, and prepares
        them for PTS processing. It handles both individual PTS files and
        compressed archives containing PTS files.
        
        Args:
            files: List of uploaded file objects from Flask/FastAPI
            
        Returns:
            Dictionary containing upload response data
        """
        try:
            self.logger.info(f"Processing upload request with {len(files)} files")
            
            # Validate upload request
            validation_result = await self._validate_upload_request(files)
            if not validation_result["valid"]:
                return self._create_error_response(
                    PTSErrorCodes.UPLOAD_ERROR,
                    validation_result["message"],
                    {"validation_errors": validation_result["errors"]}
                )
            
            # Process upload using upload service
            upload_response = await self.upload_service.handle_compressed_upload(files)
            
            self.logger.info(f"Upload completed successfully: {upload_response.upload_id}")
            
            return {
                "success": True,
                "upload_id": upload_response.upload_id,
                "files_uploaded": upload_response.files_uploaded,
                "compressed_files_extracted": upload_response.compressed_files_extracted,
                "pts_files_found": upload_response.pts_files_found,
                "upload_size": upload_response.upload_size,
                "status": upload_response.status,
                "message": upload_response.message
            }
            
        except ServiceError as e:
            self.logger.error(f"Upload service error: {e}")
            return self._create_error_response(
                PTSErrorCodes.UPLOAD_ERROR,
                str(e),
                e.details
            )
        except Exception as e:
            self.logger.error(f"Unexpected upload error: {e}")
            return self._create_error_response(
                PTSErrorCodes.INTERNAL_ERROR,
                "Upload processing failed",
                {"exception": str(e)}
            )
    
    async def handle_processing_request(self, request: PTSRenameJobRequest) -> Dict[str, Any]:
        """
        Handle PTS processing request from web interface (Direct Execution Mode)
        
        This method validates the processing request and directly processes files
        without using Dramatiq queuing, returning comprehensive results with
        detailed logs formatted for frontend display.
        
        Args:
            request: Processing request with configuration
            
        Returns:
            Dictionary containing processing results, detailed logs, and summary
        """
        try:
            self.logger.info(f"Processing request for upload {request.upload_id}")
            
            # Validate processing request
            validation_result = await self._validate_processing_request(request)
            if not validation_result["valid"]:
                return self._create_error_response(
                    PTSErrorCodes.INVALID_RENAME_PATTERN,
                    validation_result["message"],
                    {"validation_errors": validation_result["errors"]}
                )
            
            # Process files directly using core service
            self.logger.info(f"Starting direct processing for upload {request.upload_id}")
            processing_result = await self.pts_service.process_pts_files(request)
            
            # Format processing result for frontend
            formatted_result = self._format_processing_result(processing_result)
            
            self.logger.info(f"Direct processing completed for upload {request.upload_id}")
            
            return formatted_result
            
        except ServiceError as e:
            self.logger.error(f"Processing service error: {e}")
            return self._create_error_response(
                PTSErrorCodes.PROCESSING_TIMEOUT,
                str(e),
                e.details
            )
        except Exception as e:
            self.logger.error(f"Unexpected processing error: {e}")
            return self._create_error_response(
                PTSErrorCodes.INTERNAL_ERROR,
                "Processing request failed",
                {"exception": str(e)}
            )
    
    async def get_job_status(self, job_id: str) -> Dict[str, Any]:
        """
        Get processing job status for web interface
        
        Args:
            job_id: Job identifier
            
        Returns:
            Dictionary containing current job status and progress
        """
        try:
            job_status = await self.pts_service.get_job_status(job_id)
            
            if not job_status:
                return self._create_error_response(
                    PTSErrorCodes.INTERNAL_ERROR,
                    f"Job {job_id} not found"
                )
            
            return {
                "success": True,
                "job_id": job_status.job_id,
                "status": job_status.status,
                "progress": job_status.progress,
                "files_processed": job_status.files_processed,
                "total_files": job_status.total_files,
                "error_message": job_status.error_message,
                "download_url": job_status.result_download_url,
                "download_expires_at": job_status.download_expires_at.isoformat() if job_status.download_expires_at else None,
                "created_at": job_status.created_at.isoformat(),
                "updated_at": job_status.updated_at.isoformat()
            }
            
        except Exception as e:
            self.logger.error(f"Failed to get job status: {e}")
            return self._create_error_response(
                PTSErrorCodes.INTERNAL_ERROR,
                "Failed to retrieve job status",
                {"exception": str(e)}
            )
    
    async def get_preview(self, request: PTSRenamePreviewRequest) -> Dict[str, Any]:
        """
        Generate processing preview for web interface
        
        Args:
            request: Preview request with configuration
            
        Returns:
            Dictionary containing preview information
        """
        try:
            self.logger.info(f"Generating preview for upload {request.upload_id}")
            
            # Generate preview using core service
            preview = await self.pts_service.generate_preview(
                request.upload_id,
                [str(op) for op in request.operations],
                request.rename_config
            )
            
            return {
                "success": True,
                "upload_id": preview.upload_id,
                "total_files": preview.total_files,
                "files_preview": [
                    {
                        "original_name": fp.original_name,
                        "new_name": fp.new_name,
                        "qc_file_name": fp.qc_file_name,
                        "directory_name": fp.directory_name,
                        "file_size": fp.file_size,
                        "operations_applied": fp.operations_applied,
                        "warnings": fp.warnings
                    }
                    for fp in preview.files_preview
                ],
                "operations_summary": preview.operations_summary,
                "estimated_processing_time": preview.estimated_processing_time,
                "warnings": preview.warnings,
                "errors": preview.errors
            }
            
        except ServiceError as e:
            self.logger.error(f"Preview service error: {e}")
            return self._create_error_response(
                PTSErrorCodes.INTERNAL_ERROR,
                str(e),
                e.details
            )
        except Exception as e:
            self.logger.error(f"Unexpected preview error: {e}")
            return self._create_error_response(
                PTSErrorCodes.INTERNAL_ERROR,
                "Preview generation failed",
                {"exception": str(e)}
            )
    
    async def handle_download_request(self, job_id: str) -> Dict[str, Any]:
        """
        Handle download request for processed files
        
        Args:
            job_id: Job identifier
            
        Returns:
            Dictionary containing download information
        """
        try:
            job_status = await self.pts_service.get_job_status(job_id)
            
            if not job_status:
                return self._create_error_response(
                    PTSErrorCodes.INTERNAL_ERROR,
                    f"Job {job_id} not found"
                )
            
            if job_status.status != "completed":
                return self._create_error_response(
                    PTSErrorCodes.INTERNAL_ERROR,
                    f"Job {job_id} is not completed (status: {job_status.status})"
                )
            
            if not job_status.result_download_url:
                return self._create_error_response(
                    PTSErrorCodes.INTERNAL_ERROR,
                    f"Download URL not available for job {job_id}"
                )
            
            # Check if download has expired
            if job_status.download_expires_at and datetime.now() > job_status.download_expires_at:
                return self._create_error_response(
                    PTSErrorCodes.INTERNAL_ERROR,
                    "Download link has expired"
                )
            
            return {
                "success": True,
                "job_id": job_id,
                "download_url": job_status.result_download_url,
                "file_size": job_status.compressed_file_size,
                "file_name": job_status.compressed_file_name,
                "expires_at": job_status.download_expires_at.isoformat() if job_status.download_expires_at else None
            }
            
        except Exception as e:
            self.logger.error(f"Download request failed: {e}")
            return self._create_error_response(
                PTSErrorCodes.INTERNAL_ERROR,
                "Download request failed",
                {"exception": str(e)}
            )
    
    # Private helper methods
    
    async def _validate_upload_request(self, files: List[Any]) -> Dict[str, Any]:
        """Validate upload request"""
        errors = []
        
        if not files:
            errors.append("No files provided")
        
        if len(files) > 10:  # Configurable limit
            errors.append("Too many files (maximum 10 allowed)")
        
        total_size = 0
        for file in files:
            if hasattr(file, 'content_length') and file.content_length:
                total_size += file.content_length
        
        if total_size > 500 * 1024 * 1024:  # 500MB limit
            errors.append("Total upload size exceeds 500MB limit")
        
        return {
            "valid": len(errors) == 0,
            "message": "Validation failed" if errors else "Validation passed",
            "errors": errors
        }
    
    async def _validate_processing_request(self, request: PTSRenameJobRequest) -> Dict[str, Any]:
        """Validate processing request"""
        errors = []
        
        if not request.operations:
            errors.append("No operations specified")
        
        if "rename" in [str(op) for op in request.operations]:
            if not request.rename_config:
                errors.append("Rename configuration required for rename operation")
            elif not request.rename_config.get("old_pattern") or not request.rename_config.get("new_pattern"):
                errors.append("Both old_pattern and new_pattern required for rename operation")
        
        return {
            "valid": len(errors) == 0,
            "message": "Validation failed" if errors else "Validation passed",
            "errors": errors
        }
    
    def _create_error_response(self, error_code: str, message: str, details: Dict[str, Any] = None) -> Dict[str, Any]:
        """Create standardized error response"""
        return {
            "success": False,
            "error": {
                "code": error_code,
                "message": message,
                "details": details or {},
                "timestamp": datetime.now().isoformat()
            }
        }

    async def handle_delete_upload_request(self, upload_id: str) -> Dict[str, Any]:
        """
        Handle request to delete uploaded files and associated data

        Args:
            upload_id: Upload identifier

        Returns:
            Dictionary containing deletion result
        """
        try:
            self.logger.info(f"Processing delete request for upload {upload_id}")

            # Delete upload using upload service
            result = await self.upload_service.delete_upload(upload_id)

            if result.get('success'):
                self.logger.info(f"Successfully deleted upload {upload_id}")
                return {
                    'success': True,
                    'message': 'Upload deleted successfully',
                    'upload_id': upload_id,
                    'files_deleted': result.get('files_deleted', 0),
                    'directories_removed': result.get('directories_removed', 0)
                }
            else:
                self.logger.warning(f"Failed to delete upload {upload_id}: {result.get('message')}")
                return {
                    'success': False,
                    'message': result.get('message', 'Delete operation failed'),
                    'upload_id': upload_id
                }

        except Exception as e:
            self.logger.error(f"Delete upload request failed: {e}")
            return self._create_error_response(
                PTSErrorCodes.INTERNAL_ERROR,
                "Failed to delete upload",
                {"exception": str(e), "upload_id": upload_id}
            )

    def _estimate_completion_time(self, request: PTSRenameJobRequest) -> str:
        """Estimate job completion time"""
        # Simple estimation - could be made more sophisticated
        base_minutes = 2
        if request.qc_enabled:
            base_minutes += 1
        if request.create_directories:
            base_minutes += 2
        
        estimated_time = datetime.now() + timedelta(minutes=base_minutes)
        return estimated_time.isoformat()

    def _format_processing_result(self, processing_result: Dict[str, Any]) -> Dict[str, Any]:
        """
        Format processing result for frontend display with detailed logs and summary
        
        Args:
            processing_result: Raw processing result from service
            
        Returns:
            Formatted result with logs, summary, and structured data
        """
        try:
            # Extract basic information
            success = processing_result.get('success', False)
            status = processing_result.get('status', 'unknown')
            job_id = processing_result.get('job_id', '')
            total_files = processing_result.get('total_files', 0)
            processed_files = processing_result.get('processed_files', 0)
            failed_files = processing_result.get('failed_files', 0)
            processing_time = processing_result.get('processing_time', 0)
            download_url = processing_result.get('download_url')
            
            # Format processing logs for display
            raw_logs = processing_result.get('processing_logs', [])
            formatted_logs = self._format_logs_for_display(raw_logs)
            
            # Create summary information
            summary = self._create_processing_summary(processing_result)
            
            # Format detailed file results
            file_results = self._format_file_results(processing_result.get('results', []))
            
            return {
                'success': success,
                'status': status,
                'job_id': job_id,
                'summary': summary,
                'logs': formatted_logs,
                'file_results': file_results,
                'download_url': download_url,
                'processing_time': round(processing_time, 2),
                'completed_at': processing_result.get('completed_at'),
                'stats': {
                    'total_files': total_files,
                    'processed_files': processed_files,
                    'failed_files': failed_files,
                    'success_rate': round((processed_files / total_files * 100) if total_files > 0 else 0, 1)
                }
            }
            
        except Exception as e:
            self.logger.error(f"Error formatting processing result: {e}")
            return {
                'success': False,
                'status': 'format_error',
                'error': f"Failed to format result: {e}",
                'logs': [f"ERROR: Failed to format processing result: {e}"],
                'summary': "Processing completed but result formatting failed"
            }
    
    def _format_logs_for_display(self, raw_logs: List[Dict[str, Any]]) -> List[str]:
        """
        Format raw processing logs into user-friendly display format
        
        Args:
            raw_logs: List of raw log entries with timestamp, level, message
            
        Returns:
            List of formatted log strings for display
        """
        formatted_logs = []
        
        for log_entry in raw_logs:
            timestamp = log_entry.get('timestamp', '')
            level = log_entry.get('level', 'INFO')
            message = log_entry.get('message', '')
            
            # Format timestamp for display (remove microseconds)
            if timestamp:
                try:
                    from datetime import datetime
                    dt = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
                    time_str = dt.strftime('%H:%M:%S')
                except:
                    time_str = timestamp[:8] if len(timestamp) >= 8 else timestamp
            else:
                time_str = ''
            
            # Create formatted log entry
            level_icon = {
                'INFO': '📋',
                'WARNING': '⚠️',
                'ERROR': '❌',
                'DEBUG': '🔍'
            }.get(level, '📋')
            
            if time_str:
                formatted_log = f"[{time_str}] {level_icon} {message}"
            else:
                formatted_log = f"{level_icon} {message}"
            
            formatted_logs.append(formatted_log)
        
        return formatted_logs
    
    def _create_processing_summary(self, processing_result: Dict[str, Any]) -> str:
        """
        Create a human-readable summary of the processing results
        
        Args:
            processing_result: Processing result dictionary
            
        Returns:
            Summary string for display
        """
        total_files = processing_result.get('total_files', 0)
        processed_files = processing_result.get('processed_files', 0)
        failed_files = processing_result.get('failed_files', 0)
        processing_time = processing_result.get('processing_time', 0)
        
        if total_files == 0:
            return "No files were processed"
        
        if failed_files == 0:
            summary = f"Successfully processed all {total_files} files"
        else:
            summary = f"Processed {processed_files} of {total_files} files"
            if failed_files > 0:
                summary += f" ({failed_files} failed)"
        
        if processing_time > 0:
            summary += f" in {processing_time:.1f} seconds"
        
        return summary
    
    def _format_file_results(self, results: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Format individual file processing results for display
        
        Args:
            results: List of file processing results
            
        Returns:
            List of formatted file results
        """
        formatted_results = []
        
        for result in results:
            formatted_result = {
                'filename': result.get('filename', 'Unknown'),
                'status': result.get('status', 'unknown'),
                'original_path': result.get('original_path', ''),
                'final_path': result.get('final_file_path', ''),
                'operations': result.get('operations_performed', []),
                'output_files': result.get('output_files', [])
            }
            
            # Add path changes information if available
            path_changes = result.get('path_changes', [])
            if path_changes:
                formatted_result['path_changes'] = path_changes
            
            # Add error information if failed
            if result.get('status') == 'failed' and result.get('error'):
                formatted_result['error'] = result.get('error')
            
            formatted_results.append(formatted_result)
        
        return formatted_results


class PresenterError(Exception):
    """Exception raised by presenter operations"""
    
    def __init__(self, message: str, error_code: str = None, details: Dict[str, Any] = None):
        super().__init__(message)
        self.error_code = error_code or PTSErrorCodes.INTERNAL_ERROR
        self.details = details or {}
        self.timestamp = datetime.now()