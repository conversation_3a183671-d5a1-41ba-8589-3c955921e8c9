"""
PTS Renamer Upload Service

This service handles compressed file uploads for the PTS Renamer module.
It integrates with existing Dramatiq decompression tasks and provides
secure file validation and processing.

Features:
- Support for ZIP, 7Z, RAR file formats
- Integration with existing Dramatiq decompression infrastructure
- Secure file validation and malicious content scanning
- Automatic PTS file detection and extraction
- Upload progress tracking and status monitoring
"""

import os
import uuid
import hashlib
import tempfile
import shutil
from datetime import datetime, timedelta
from pathlib import Path
from typing import List, Dict, Any, Optional, Tuple, Union
import asyncio
import mimetypes

from loguru import logger

# Import existing infrastructure
from backend.shared.infrastructure.adapters.file_staging_service import FileStagingService
from backend.tasks.archive_pipeline_tasks import is_archive_file, get_supported_archive_formats
from backend.file_management.adapters.file_upload.archive_extractor import ArchiveExtractor
from backend.pts_renamer.models.pts_rename_models import (
    PTSFileUploadInfo, PTSRenameUploadRequest, PTSRenameUploadResponse,
    PTSRenameErrorResponse, PTSErrorCodes, PTSRenameSecurityValidation,
    PTSRenameConfig
)
from backend.pts_renamer.models.pts_rename_entities import PTSFile, PTSProcessingJob
from backend.pts_renamer.repositories.pts_rename_repository import IPTSRenameRepository


class PTSRenameUploadService:
    """
    PTS Renamer Upload Service
    
    Handles compressed file uploads with security validation,
    decompression using existing Dramatiq tasks, and PTS file detection.
    """
    
    def __init__(
        self,
        repository: IPTSRenameRepository,
        staging_service: FileStagingService,
        config: PTSRenameConfig,
        temp_storage_path: str = None
    ):
        """
        Initialize the upload service
        
        Args:
            repository: PTS rename repository for data persistence
            staging_service: File staging service for temporary storage
            config: PTS renamer configuration
            temp_storage_path: Temporary storage path override
        """
        self.repository = repository
        self.staging_service = staging_service
        self.config = config
        
        # Use configuration from file_management system (.env variables)
        # Priority: explicit override > config property > fallback default
        if temp_storage_path:
            temp_path = temp_storage_path
        elif config and hasattr(config, 'temp_storage_path'):
            temp_path = config.temp_storage_path  # Uses UploadConfig from file_management
        else:
            temp_path = "d:/temp/uploads"  # Fallback default
            
        # Ensure temp_path is a string before converting to Path
        if isinstance(temp_path, Path):
            temp_path = str(temp_path)
        self.temp_storage_path = Path(temp_path)
        self.temp_storage_path.mkdir(parents=True, exist_ok=True)
        
        # Supported formats from existing infrastructure
        self.supported_formats = get_supported_archive_formats()
        self.pts_extensions = ['.pts']
        
        logger.info(f"PTS Upload Service initialized with temp storage: {self.temp_storage_path}")
        logger.info(f"Supported archive formats: {self.supported_formats}")
    
    async def handle_compressed_upload(
        self,
        files: List[Union[bytes, Any]],
        extract_compressed: bool = True,
        validate_pts_files: bool = True,
        user_id: Optional[str] = None
    ) -> PTSRenameUploadResponse:
        """
        Handle compressed file upload with validation and extraction
        
        Args:
            files: List of uploaded files (bytes or file objects)
            extract_compressed: Whether to extract compressed files
            validate_pts_files: Whether to validate PTS file format
            user_id: User ID for audit trail
            
        Returns:
            PTSRenameUploadResponse: Upload result with upload_id
        """
        upload_id = f"pts_upload_{uuid.uuid4().hex[:12]}"
        upload_start_time = datetime.now()
        
        logger.info(f"[UPLOAD] Starting upload processing: {upload_id}")
        logger.info(f"   Files count: {len(files)}")
        logger.info(f"   Extract compressed: {extract_compressed}")
        logger.info(f"   Validate PTS: {validate_pts_files}")
        
        try:
            # Step 1: Validate and save uploaded files
            upload_info_list = []
            total_upload_size = 0
            
            for i, file_data in enumerate(files):
                try:
                    file_info = await self._process_single_upload(
                        file_data, upload_id, i, user_id
                    )
                    upload_info_list.append(file_info)
                    total_upload_size += file_info.size
                    
                except Exception as e:
                    logger.error(f"[UPLOAD] Failed to process file {i}: {e}")
                    return PTSRenameUploadResponse(
                        upload_id=upload_id,
                        files_uploaded=0,
                        compressed_files_extracted=0,
                        pts_files_found=0,
                        upload_size=0,
                        status="failed",
                        message=f"File processing failed: {str(e)}",
                        warnings=[f"Failed to process file {i}: {str(e)}"]
                    )
            
            # Step 2: Validate total upload size
            if total_upload_size > self.config.max_total_upload_size_mb * 1024 * 1024:
                error_msg = f"Total upload size ({total_upload_size / 1024 / 1024:.1f}MB) exceeds limit ({self.config.max_total_upload_size_mb}MB)"
                logger.error(f"[UPLOAD] {error_msg}")
                
                # Cleanup uploaded files
                await self._cleanup_upload_files(upload_id)
                
                return PTSRenameUploadResponse(
                    upload_id=upload_id,
                    files_uploaded=0,
                    compressed_files_extracted=0,
                    pts_files_found=0,
                    upload_size=total_upload_size,
                    status="failed",
                    message=error_msg
                )
            
            # Step 3: Extract compressed files if requested
            compressed_files_extracted = 0
            pts_files_found = 0
            warnings = []
            
            if extract_compressed:
                extraction_results = await self._extract_compressed_files(
                    upload_info_list, upload_id
                )
                compressed_files_extracted = extraction_results['extracted_count']
                pts_files_found = extraction_results['pts_files_found']
                warnings.extend(extraction_results['warnings'])
            else:
                # Count PTS files in uploaded files
                pts_files_found = sum(
                    1 for info in upload_info_list 
                    if Path(info.filename).suffix.lower() in self.pts_extensions
                )
            
            # Step 4: Validate PTS files if requested
            if validate_pts_files and pts_files_found > 0:
                validation_results = await self._validate_pts_files(upload_id)
                warnings.extend(validation_results['warnings'])
            
            # Step 5: Create upload record in repository
            upload_record = {
                'upload_id': upload_id,
                'files_uploaded': len(upload_info_list),
                'compressed_files_extracted': compressed_files_extracted,
                'pts_files_found': pts_files_found,
                'upload_size': total_upload_size,
                'user_id': user_id,
                # Don't include created_at - let SQLAlchemy use default datetime.now
                'status': 'completed'
            }
            
            logger.critical(f"🚨 [INCIDENT_DEBUG] About to call save_upload_record")
            logger.critical(f"🚨 [INCIDENT_DEBUG] Repository type: {type(self.repository)}")
            logger.critical(f"🚨 [INCIDENT_DEBUG] Repository module: {self.repository.__class__.__module__}")
            logger.critical(f"🚨 [INCIDENT_DEBUG] Upload record: {upload_record}")
            
            try:
                await self.repository.save_upload_record(upload_record)
                logger.critical(f"🚨 [INCIDENT_DEBUG] save_upload_record completed successfully")
            except Exception as e:
                logger.critical(f"🚨 [INCIDENT_DEBUG] save_upload_record FAILED: {e}")
                logger.critical(f"🚨 [INCIDENT_DEBUG] Exception type: {type(e)}")
                logger.critical(f"🚨 [INCIDENT_DEBUG] Exception args: {e.args}")
                raise
                
            # Step 6: CRITICAL FIX - Extract and save PTS files to database
            logger.critical(f"🚨 [CRITICAL_FIX] Starting PTS files extraction for database storage...")
            print(f"🔥 [DEBUG] CRITICAL FIX REACHED - Extracting PTS files for upload_id: {upload_id}")
            try:
                extracted_pts_files = await self.extract_pts_files(upload_id)
                logger.critical(f"🚨 [CRITICAL_FIX] Extracted {len(extracted_pts_files)} PTS files from upload directory")
                print(f"🔥 [DEBUG] Extracted {len(extracted_pts_files)} PTS files")
                
                if extracted_pts_files:
                    # Save PTS files to database for later processing
                    for pts_file in extracted_pts_files:
                        try:
                            await self.repository.save_pts_file(pts_file)
                            logger.debug(f"[UPLOAD] Saved PTS file to database: {pts_file.filename}")
                        except Exception as e:
                            logger.warning(f"[UPLOAD] Failed to save PTS file {pts_file.filename}: {e}")
                    
                    logger.info(f"[UPLOAD] Successfully saved {len(extracted_pts_files)} PTS files to database")
                else:
                    logger.warning(f"[UPLOAD] No PTS files found during extraction - this may cause processing issues")
                    
            except Exception as e:
                logger.error(f"[UPLOAD] Failed to extract/save PTS files: {e}")
                # Don't fail the upload, but warn about potential processing issues
                warnings.append(f"Warning: Failed to save PTS files to database - processing may fail")
                import traceback
                logger.error(f"[UPLOAD] PTS extraction traceback: {traceback.format_exc()}")
            
            processing_time = (datetime.now() - upload_start_time).total_seconds()
            
            logger.info(f"[UPLOAD] Upload completed: {upload_id}")
            logger.info(f"   Files uploaded: {len(upload_info_list)}")
            logger.info(f"   Compressed extracted: {compressed_files_extracted}")
            logger.info(f"   PTS files found: {pts_files_found}")
            logger.info(f"   Processing time: {processing_time:.2f}s")
            
            return PTSRenameUploadResponse(
                upload_id=upload_id,
                files_uploaded=len(upload_info_list),
                compressed_files_extracted=compressed_files_extracted,
                pts_files_found=pts_files_found,
                upload_size=total_upload_size,
                status="completed",
                message=f"Upload completed successfully. Found {pts_files_found} PTS files.",
                warnings=warnings,
                file_details=upload_info_list
            )
            
        except Exception as e:
            logger.error(f"[UPLOAD] Upload processing failed: {e}")
            
            # Cleanup on failure
            await self._cleanup_upload_files(upload_id)
            
            return PTSRenameUploadResponse(
                upload_id=upload_id,
                files_uploaded=0,
                compressed_files_extracted=0,
                pts_files_found=0,
                upload_size=0,
                status="failed",
                message=f"Upload processing failed: {str(e)}"
            )
    
    async def _process_single_upload(
        self,
        file_data: Union[bytes, Any],
        upload_id: str,
        file_index: int,
        user_id: Optional[str] = None
    ) -> PTSFileUploadInfo:
        """
        Process a single uploaded file
        
        Args:
            file_data: File data (bytes or file object)
            upload_id: Upload identifier
            file_index: File index in upload batch
            user_id: User ID for audit trail
            
        Returns:
            PTSFileUploadInfo: File upload information
        """
        # Extract file information
        if hasattr(file_data, 'filename'):
            filename = file_data.filename
            content_type = getattr(file_data, 'content_type', None)
            file_content = file_data.read() if hasattr(file_data, 'read') else file_data
        else:
            filename = f"uploaded_file_{file_index}"
            content_type = None
            file_content = file_data if isinstance(file_data, bytes) else bytes(file_data)
        
        # Validate filename
        if not filename or len(filename.strip()) == 0:
            filename = f"uploaded_file_{file_index}"
        
        # Detect content type if not provided
        if not content_type:
            content_type, _ = mimetypes.guess_type(filename)
            if not content_type:
                content_type = "application/octet-stream"
        
        file_size = len(file_content)
        
        # Validate file size
        if file_size > self.config.max_file_size_mb * 1024 * 1024:
            raise ValueError(f"File {filename} exceeds size limit ({self.config.max_file_size_mb}MB)")
        
        # Calculate checksum
        checksum = hashlib.sha256(file_content).hexdigest()
        
        # Determine if file is compressed
        is_compressed = is_archive_file(filename)
        
        # Security validation
        security_validation = await self._validate_file_security(
            filename, content_type, file_size, checksum, file_content
        )
        
        if not security_validation.is_safe:
            threats = ", ".join(security_validation.threats_detected)
            raise ValueError(f"Security validation failed for {filename}: {threats}")
        
        # Save file to temporary storage
        upload_dir = self.temp_storage_path / upload_id
        upload_dir.mkdir(parents=True, exist_ok=True)
        
        file_path = upload_dir / filename
        with open(file_path, 'wb') as f:
            f.write(file_content)
        
        logger.debug(f"[UPLOAD] Saved file: {filename} ({file_size} bytes)")
        
        return PTSFileUploadInfo(
            filename=filename,
            size=file_size,
            content_type=content_type,
            checksum=checksum,
            is_compressed=is_compressed
        )
    
    async def _extract_compressed_files(
        self,
        upload_info_list: List[PTSFileUploadInfo],
        upload_id: str
    ) -> Dict[str, Any]:
        """
        Extract compressed files using Dramatiq extract_archive_task
        
        This method now uses the existing Dramatiq extract_archive_task for 
        asynchronous extraction processing, providing better consistency 
        with the compression workflow.
        
        Args:
            upload_info_list: List of uploaded file information
            upload_id: Upload identifier
            
        Returns:
            Dict with extraction results
        """
        extracted_count = 0
        pts_files_found = 0
        warnings = []
        
        upload_dir = self.temp_storage_path / upload_id
        
        for file_info in upload_info_list:
            if file_info.is_compressed:
                try:
                    archive_path = upload_dir / file_info.filename
                    extract_to = upload_dir / f"{Path(file_info.filename).stem}_extracted"
                    
                    logger.info(f"[EXTRACT] Using Dramatiq extract_archive_task for: {file_info.filename}")
                    
                    # Use Dramatiq extract_archive_task for asynchronous processing
                    try:
                        # Import the Dramatiq task
                        from backend.tasks.archive_pipeline_tasks import extract_archive_task
                        
                        # Submit extraction task and wait for result
                        task_message = extract_archive_task.send(
                            str(archive_path),
                            str(extract_to),
                            pipeline_context={
                                'upload_id': upload_id,
                                'filename': file_info.filename,
                                'task_type': 'pts_upload_extraction'
                            }
                        )
                        
                        logger.info(f"[EXTRACT] Dramatiq task submitted: {task_message.message_id}")
                        
                        # Wait for task completion with timeout
                        import time
                        max_wait_time = 300  # 5 minutes (increased for larger files)
                        wait_interval = 1  # 1 second (more responsive)
                        elapsed_time = 0
                        
                        extraction_result = None
                        logger.info(f"[EXTRACT] Waiting for extraction to complete at: {extract_to}")
                        
                        while elapsed_time < max_wait_time:
                            try:
                                # Check if extraction directory exists and has content
                                if extract_to and Path(extract_to).exists():
                                    # Build result from filesystem
                                    extracted_files = []
                                    for root, dirs, files in os.walk(extract_to):
                                        for file in files:
                                            extracted_files.append(os.path.join(root, file))
                                    
                                    if extracted_files:  # Only consider success if files were extracted
                                        extraction_result = {
                                            'success': True,
                                            'extracted_path': str(extract_to),
                                            'temp_path': str(extract_to),
                                            'extracted_files': extracted_files,
                                            'extract_dir': str(extract_to)
                                        }
                                        logger.info(f"[EXTRACT] PTS Dramatiq task completed: {len(extracted_files)} files extracted to {extract_to}")
                                        break
                                    else:
                                        logger.debug(f"[EXTRACT] Directory exists but no files found yet at {extract_to}")
                                else:
                                    logger.debug(f"[EXTRACT] Directory not found yet at {extract_to}")
                                
                                time.sleep(wait_interval)
                                elapsed_time += wait_interval
                                
                            except Exception as wait_error:
                                logger.warning(f"[EXTRACT] Error checking task result: {wait_error}")
                                time.sleep(wait_interval)
                                elapsed_time += wait_interval
                        
                        # Handle timeout or no result
                        if extraction_result is None:
                            logger.error(f"[EXTRACT] PTS task timeout or failed after {max_wait_time}s for {file_info.filename}")
                            logger.error(f"[EXTRACT] Expected extraction path: {extract_to}")
                            extraction_result = {
                                'success': False,
                                'error': f'PTS extraction task timeout or failed after {max_wait_time} seconds'
                            }
                        
                        # Ensure proper result format
                        if extraction_result.get('success', False):
                            # Update path fields to match expected format
                            extraction_result['extracted_path'] = extraction_result.get('extracted_path') or str(extract_to)
                            extraction_result['temp_path'] = extraction_result.get('temp_path') or str(extract_to)
                        else:
                            if not extraction_result.get('error'):
                                extraction_result['error'] = 'Archive extraction failed via Dramatiq'
                            
                    except Exception as e:
                        logger.error(f"[EXTRACT] Dramatiq task error for {file_info.filename}: {e}")
                        extraction_result = {
                            'success': False,
                            'error': f'Dramatiq extraction task exception: {str(e)}'
                        }
                    
                    if extraction_result.get('success', False):
                        extracted_count += 1
                        
                        # Count PTS files in extracted content
                        extracted_files = extraction_result.get('extracted_files', [])
                        pts_count = sum(
                            1 for f in extracted_files 
                            if Path(f).suffix.lower() in self.pts_extensions
                        )
                        pts_files_found += pts_count
                        
                        # Update file info with extracted files
                        file_info.extracted_files = extracted_files
                        
                        logger.info(f"[EXTRACT] Extracted {len(extracted_files)} files, {pts_count} PTS files")
                        
                    else:
                        error_msg = extraction_result.get('error', 'Unknown extraction error')
                        warnings.append(f"Failed to extract {file_info.filename}: {error_msg}")
                        logger.warning(f"[EXTRACT] Failed: {file_info.filename} - {error_msg}")
                        
                except Exception as e:
                    warnings.append(f"Extraction error for {file_info.filename}: {str(e)}")
                    logger.error(f"[EXTRACT] Error extracting {file_info.filename}: {e}")
            else:
                # Count PTS files in non-compressed uploads
                if Path(file_info.filename).suffix.lower() in self.pts_extensions:
                    pts_files_found += 1
        
        return {
            'extracted_count': extracted_count,
            'pts_files_found': pts_files_found,
            'warnings': warnings
        }
    
    async def _validate_file_security(
        self,
        filename: str,
        content_type: str,
        file_size: int,
        checksum: str,
        file_content: bytes
    ) -> PTSRenameSecurityValidation:
        """
        Validate file security
        
        Args:
            filename: File name
            content_type: MIME content type
            file_size: File size in bytes
            checksum: File checksum
            file_content: File content bytes
            
        Returns:
            PTSRenameSecurityValidation: Security validation result
        """
        threats_detected = []
        is_safe = True
        
        try:
            # 1. File extension validation
            file_path = Path(filename)
            if file_path.suffix.lower() not in (self.config.supported_formats + self.pts_extensions):
                threats_detected.append(f"Unsupported file extension: {file_path.suffix}")
                is_safe = False
            
            # 2. Filename validation (prevent path traversal)
            if '..' in filename or filename.startswith('/') or ':' in filename:
                threats_detected.append("Dangerous filename pattern detected")
                is_safe = False
            
            # 3. Content type validation
            allowed_content_types = [
                'application/zip',
                'application/x-zip-compressed',
                'application/x-7z-compressed',
                'application/x-rar-compressed',
                'application/octet-stream',
                'text/plain'
            ]
            
            if content_type and content_type not in allowed_content_types:
                # Warning but not blocking
                logger.warning(f"[SECURITY] Unusual content type: {content_type} for {filename}")
            
            # 4. File size validation (already done in caller)
            
            # 5. Basic malicious content detection
            if self.config.enable_security_scanning:
                # Skip content pattern scanning for binary archive files
                # Binary archives can contain any byte sequence as legitimate compressed data
                skip_content_scanning = is_archive_file(filename)
                
                if not skip_content_scanning:
                    malicious_patterns = [
                        b'<script',
                        b'javascript:',
                        b'vbscript:',
                        b'<?php',
                        b'<%',
                        b'eval(',
                        b'exec(',
                        b'system(',
                        b'shell_exec('
                    ]
                    
                    content_lower = file_content.lower()
                    for pattern in malicious_patterns:
                        if pattern in content_lower:
                            threats_detected.append(f"Suspicious content pattern detected: {pattern.decode('utf-8', errors='ignore')}")
                            is_safe = False
                            break
                else:
                    logger.debug(f"[SECURITY] Skipping content pattern scanning for binary archive: {filename}")
            
            # 6. File header validation for archives
            if is_archive_file(filename):
                if not self._validate_archive_header(file_content, filename):
                    threats_detected.append("Invalid archive file header")
                    is_safe = False
            
            return PTSRenameSecurityValidation(
                file_path=filename,
                content_type=content_type,
                file_size=file_size,
                checksum=checksum,
                is_safe=is_safe,
                threats_detected=threats_detected
            )
            
        except Exception as e:
            logger.error(f"[SECURITY] Security validation error: {e}")
            return PTSRenameSecurityValidation(
                file_path=filename,
                content_type=content_type,
                file_size=file_size,
                checksum=checksum,
                is_safe=False,
                threats_detected=[f"Security validation error: {str(e)}"]
            )
    
    def _validate_archive_header(self, file_content: bytes, filename: str) -> bool:
        """
        Validate archive file headers
        
        Args:
            file_content: File content bytes
            filename: File name
            
        Returns:
            bool: True if header is valid
        """
        if len(file_content) < 10:
            return False
        
        file_ext = Path(filename).suffix.lower()
        header = file_content[:10]
        
        # ZIP file signatures
        if file_ext == '.zip':
            return header.startswith(b'PK\x03\x04') or header.startswith(b'PK\x05\x06')
        
        # 7Z file signature
        elif file_ext == '.7z':
            return header.startswith(b'7z\xbc\xaf\x27\x1c')
        
        # RAR file signatures
        elif file_ext == '.rar':
            return header.startswith(b'Rar!\x1a\x07\x00') or header.startswith(b'Rar!\x1a\x07\x01')
        
        # For other formats, assume valid (basic check)
        return True
    
    async def _validate_pts_files(self, upload_id: str) -> Dict[str, Any]:
        """
        Validate PTS files in upload
        
        Args:
            upload_id: Upload identifier
            
        Returns:
            Dict with validation results
        """
        warnings = []
        upload_dir = self.temp_storage_path / upload_id
        
        try:
            # Find all PTS files (including extracted ones)
            pts_files = []
            for file_path in upload_dir.rglob('*.pts'):
                pts_files.append(file_path)
            
            logger.info(f"[VALIDATE] Found {len(pts_files)} PTS files for validation")
            
            for pts_file in pts_files:
                try:
                    # Basic PTS file validation
                    if not self._validate_single_pts_file(pts_file):
                        warnings.append(f"Invalid PTS file format: {pts_file.name}")
                        
                except Exception as e:
                    warnings.append(f"PTS validation error for {pts_file.name}: {str(e)}")
                    logger.warning(f"[VALIDATE] Error validating {pts_file.name}: {e}")
            
            return {
                'pts_files_validated': len(pts_files),
                'warnings': warnings
            }
            
        except Exception as e:
            logger.error(f"[VALIDATE] PTS validation error: {e}")
            return {
                'pts_files_validated': 0,
                'warnings': [f"PTS validation error: {str(e)}"]
            }
    
    def _validate_single_pts_file(self, pts_file_path: Path) -> bool:
        """
        Validate a single PTS file
        
        Args:
            pts_file_path: Path to PTS file
            
        Returns:
            bool: True if valid PTS file
        """
        try:
            # Basic PTS file validation
            # Check file size (should not be empty)
            if pts_file_path.stat().st_size == 0:
                return False
            
            # Read first few lines to check format
            with open(pts_file_path, 'r', encoding='utf-8', errors='ignore') as f:
                lines = [f.readline().strip() for _ in range(10)]
            
            # Look for common PTS file patterns
            content = '\n'.join(lines).lower()
            
            # Common PTS file indicators
            pts_indicators = [
                'parameter',
                'bin definition',
                'test',
                'limit',
                'result',
                'qa',
                'qc'
            ]
            
            # Check if any indicator is present
            has_indicator = any(indicator in content for indicator in pts_indicators)
            
            return has_indicator
            
        except Exception as e:
            logger.debug(f"[VALIDATE] PTS file validation error: {e}")
            return False
    
    async def extract_pts_files(self, upload_id: str) -> List[PTSFile]:
        """
        Extract PTS files from upload directory
        
        Args:
            upload_id: Upload identifier
            
        Returns:
            List[PTSFile]: List of PTS file entities
        """
        upload_dir = self.temp_storage_path / upload_id
        pts_files = []
        
        try:
            # Find all PTS and CPTS files
            pts_patterns = ['*.pts', '*.cpts']
            for pattern in pts_patterns:
                for file_path in upload_dir.rglob(pattern):
                    try:
                        # Use factory method to create PTSFile with proper types and upload_id
                        pts_file = PTSFile.create_from_path(
                            file_path=file_path,
                            upload_id=upload_id,
                            extracted_from=None  # Could be set to original archive name if available
                        )
                        
                        pts_files.append(pts_file)
                        
                    except Exception as e:
                        logger.warning(f"[EXTRACT] Error processing PTS file {file_path}: {e}")
            
            logger.info(f"[EXTRACT] Found {len(pts_files)} PTS/CPTS files in upload {upload_id}")
            return pts_files
            
        except Exception as e:
            logger.error(f"[EXTRACT] Error extracting PTS files: {e}")
            return []
    
    async def _calculate_file_checksum(self, file_path: Path) -> str:
        """
        Calculate file checksum asynchronously
        
        Args:
            file_path: Path to file
            
        Returns:
            str: SHA-256 checksum
        """
        def _calc_checksum():
            hash_sha256 = hashlib.sha256()
            with open(file_path, 'rb') as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    hash_sha256.update(chunk)
            return hash_sha256.hexdigest()
        
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(None, _calc_checksum)
    
    async def validate_uploaded_files(
        self,
        files: List[Any]
    ) -> Tuple[bool, str]:
        """
        Validate uploaded files before processing
        
        Args:
            files: List of uploaded files
            
        Returns:
            Tuple[bool, str]: (is_valid, error_message)
        """
        try:
            # Check file count
            if len(files) > self.config.max_files_per_upload:
                return False, f"Too many files (max {self.config.max_files_per_upload})"
            
            if len(files) == 0:
                return False, "No files uploaded"
            
            # Check total size estimate
            total_size = 0
            for file_data in files:
                if hasattr(file_data, 'size'):
                    total_size += file_data.size
                elif hasattr(file_data, '__len__'):
                    total_size += len(file_data)
            
            max_total_size = self.config.max_total_upload_size_mb * 1024 * 1024
            if total_size > max_total_size:
                return False, f"Total upload size too large ({total_size / 1024 / 1024:.1f}MB > {self.config.max_total_upload_size_mb}MB)"
            
            return True, "Validation passed"
            
        except Exception as e:
            return False, f"Validation error: {str(e)}"
    
    async def _cleanup_upload_files(self, upload_id: str) -> bool:
        """
        Cleanup uploaded files
        
        Args:
            upload_id: Upload identifier
            
        Returns:
            bool: True if cleanup successful
        """
        try:
            upload_dir = self.temp_storage_path / upload_id
            if upload_dir.exists():
                shutil.rmtree(upload_dir)
                logger.info(f"[CLEANUP] Cleaned up upload directory: {upload_id}")
                return True
            return True
            
        except Exception as e:
            logger.error(f"[CLEANUP] Failed to cleanup upload {upload_id}: {e}")
            return False
    
    async def cleanup_expired_uploads(self, max_age_hours: int = 24) -> int:
        """
        Cleanup expired upload files
        
        Args:
            max_age_hours: Maximum age in hours
            
        Returns:
            int: Number of uploads cleaned up
        """
        cleaned_count = 0
        cutoff_time = datetime.now() - timedelta(hours=max_age_hours)
        
        try:
            for upload_dir in self.temp_storage_path.iterdir():
                if upload_dir.is_dir() and upload_dir.name.startswith('pts_upload_'):
                    try:
                        # Check directory creation time
                        dir_stat = upload_dir.stat()
                        dir_time = datetime.fromtimestamp(dir_stat.st_ctime)
                        
                        if dir_time < cutoff_time:
                            shutil.rmtree(upload_dir)
                            cleaned_count += 1
                            logger.debug(f"[CLEANUP] Cleaned expired upload: {upload_dir.name}")
                            
                    except Exception as e:
                        logger.warning(f"[CLEANUP] Error cleaning {upload_dir.name}: {e}")
            
            if cleaned_count > 0:
                logger.info(f"[CLEANUP] Cleaned up {cleaned_count} expired uploads")
            
            return cleaned_count
            
        except Exception as e:
            logger.error(f"[CLEANUP] Error during cleanup: {e}")
            return 0
    
    def get_upload_info(self, upload_id: str) -> Optional[Dict[str, Any]]:
        """
        Get upload information
        
        Args:
            upload_id: Upload identifier
            
        Returns:
            Optional[Dict]: Upload information or None if not found
        """
        try:
            upload_dir = self.temp_storage_path / upload_id
            if not upload_dir.exists():
                return None
            
            # Count files
            all_files = list(upload_dir.rglob('*'))
            file_count = sum(1 for f in all_files if f.is_file())
            pts_count = sum(1 for f in all_files if f.is_file() and f.suffix.lower() == '.pts')
            
            # Calculate total size
            total_size = sum(f.stat().st_size for f in all_files if f.is_file())
            
            # Get creation time
            dir_stat = upload_dir.stat()
            created_at = datetime.fromtimestamp(dir_stat.st_ctime)
            
            return {
                'upload_id': upload_id,
                'upload_dir': str(upload_dir),
                'file_count': file_count,
                'pts_file_count': pts_count,
                'total_size': total_size,
                'created_at': created_at.isoformat(),
                'exists': True
            }
            
        except Exception as e:
            logger.error(f"[INFO] Error getting upload info for {upload_id}: {e}")
            return None

    async def delete_upload(self, upload_id: str) -> Dict[str, Any]:
        """
        Delete uploaded files and associated database records

        Args:
            upload_id: Upload identifier

        Returns:
            Dictionary containing deletion result
        """
        try:
            self.logger.info(f"[DELETE] Starting deletion for upload {upload_id}")

            files_deleted = 0
            directories_removed = 0

            # 1. Get upload directory path
            upload_dir = self.temp_storage_path / upload_id

            if upload_dir.exists():
                self.logger.info(f"[DELETE] Found upload directory: {upload_dir}")

                # Count files before deletion
                all_files = list(upload_dir.rglob("*"))
                files_deleted = len([f for f in all_files if f.is_file()])

                # Remove the entire upload directory
                import shutil
                shutil.rmtree(upload_dir)
                directories_removed = 1

                self.logger.info(f"[DELETE] Removed upload directory with {files_deleted} files")
            else:
                self.logger.warning(f"[DELETE] Upload directory not found: {upload_dir}")

            # 2. Delete database records
            try:
                await self.repository.delete_upload_record(upload_id)
                self.logger.info(f"[DELETE] Deleted database records for upload {upload_id}")
            except Exception as db_error:
                self.logger.warning(f"[DELETE] Failed to delete database records: {db_error}")
                # Continue with file deletion even if DB deletion fails

            # 3. Clean up any staging files (if method exists)
            try:
                if hasattr(self.staging_service, 'cleanup_upload'):
                    staging_result = await self.staging_service.cleanup_upload(upload_id)
                    if staging_result.get('success'):
                        self.logger.info(f"[DELETE] Cleaned up staging files for upload {upload_id}")
                else:
                    self.logger.debug(f"[DELETE] Staging service does not have cleanup_upload method")
            except Exception as staging_error:
                self.logger.warning(f"[DELETE] Failed to clean up staging files: {staging_error}")

            self.logger.info(f"[DELETE] Deletion completed for upload {upload_id}")

            return {
                'success': True,
                'message': 'Upload deleted successfully',
                'upload_id': upload_id,
                'files_deleted': files_deleted,
                'directories_removed': directories_removed
            }

        except Exception as e:
            self.logger.error(f"[DELETE] Failed to delete upload {upload_id}: {e}")
            return {
                'success': False,
                'message': f'Failed to delete upload: {str(e)}',
                'upload_id': upload_id
            }