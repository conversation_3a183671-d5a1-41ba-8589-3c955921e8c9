# Containerfile for Outlook Summary System - Podman Compatible
# 半導體測試數據自動化郵件處理系統 - 容器化部署配置
# 支持 Flask + FastAPI + Dramatiq Worker 一體化部署

FROM python:3.11.9-slim

LABEL maintainer="Outlook Summary System" \
      version="1.0" \
      description="半導體測試數據郵件處理系統容器化部署"

# 設置工作目錄
WORKDIR /app

# 設置環境變量
ENV PYTHONPATH=/app \
    PYTHONIOENCODING=utf-8 \
    PYTHONUTF8=1 \
    FLASK_ENV=production \
    FLASK_PORT=5000 \
    FASTAPI_PORT=5001 \
    USE_MEMORY_BROKER=false \
    DEBIAN_FRONTEND=noninteractive

# 安裝系統依賴
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    libffi-dev \
    libssl-dev \
    curl \
    redis-tools \
    && rm -rf /var/lib/apt/lists/*

# 複製 requirements 文件並安裝 Python 依賴
COPY requirements.txt .
RUN pip install --no-cache-dir --upgrade pip && \
    pip install --no-cache-dir -r requirements.txt

# 複製專案檔案
COPY . .

# 創建必要的目錄
RUN mkdir -p /app/data /app/logs /app/uploads /app/temp

# 設置文件權限
RUN chmod +x start_integrated_services.py && \
    chmod +x dramatiq_tasks.py

# 建立啟動腳本以統一在 port 5000 運行所有服務
RUN cat > /app/container_startup.sh << 'EOF'
#!/bin/bash
set -e

echo "🚀 正在啟動容器化 Outlook Summary 系統..."
echo "📧 Flask 前端: http://localhost:5000"
echo "🔧 Dramatiq Worker: 背景運行"

# 等待 Redis 連接就緒 (如果使用外部 Redis)
if [ "$USE_MEMORY_BROKER" = "false" ]; then
    echo "⏳ 等待 Redis 連接..."
    until redis-cli -h ${REDIS_HOST:-localhost} -p ${REDIS_PORT:-6379} ping; do
        echo "Redis 未就緒，等待中..."
        sleep 2
    done
    echo "✅ Redis 連接成功"
fi

# 設置統一端口為 5000
export FLASK_PORT=5000
export FASTAPI_PORT=5001

# 啟動應用程式
echo "🎯 啟動整合服務 (Port 5000)..."
python start_integrated_services.py --flask-port 5000 --fastapi-port 5001 --mode integrated
EOF

RUN chmod +x /app/container_startup.sh

# 健康檢查
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:5000/health || exit 1

# 暴露端口
EXPOSE 5000

# 設置入口點
ENTRYPOINT ["/app/container_startup.sh"]