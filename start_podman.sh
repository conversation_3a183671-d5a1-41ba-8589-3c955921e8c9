#!/bin/bash
# Bash 腳本 - Podman 容器化 Outlook Summary 系統啟動
# 半導體測試數據自動化郵件處理系統 - 一鍵部署腳本

set -e

# 顏色設定
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
WHITE='\033[0;37m'
NC='\033[0m' # No Color

# 參數設定
MODE=${1:-up}
BACKGROUND=${2:-false}
FORCE=${3:-false}

# 顏色輸出函數
print_color() {
    local message="$1"
    local color="$2"
    echo -e "${color}${message}${NC}"
}

show_header() {
    print_color "============================================================" "$BLUE"
    print_color "🐧 Podman 容器化 - Outlook Summary 系統" "$CYAN"
    print_color "半導體測試數據自動化郵件處理系統" "$WHITE"
    print_color "============================================================" "$BLUE"
    print_color "📧 主要服務: http://localhost:5000" "$GREEN"
    print_color "🔧 Dramatiq Worker: 背景運行" "$GREEN"
    print_color "💾 Redis: 內建任務佇列" "$GREEN"
    print_color "============================================================" "$BLUE"
}

test_podman_installed() {
    if command -v podman &> /dev/null; then
        local version=$(podman --version)
        print_color "✅ 檢測到 Podman: $version" "$GREEN"
        return 0
    else
        print_color "❌ 錯誤: 未檢測到 Podman 安裝" "$RED"
        print_color "請先安裝 Podman: https://podman.io/getting-started/installation" "$YELLOW"
        return 1
    fi
}

test_env_file() {
    if [ ! -f ".env" ]; then
        print_color "⚠️  警告: 未找到 .env 文件" "$YELLOW"
        print_color "正在創建範例 .env 文件..." "$YELLOW"
        
        cat > .env << 'EOF'
# 郵件配置 (必要)
EMAIL_ADDRESS=<EMAIL>
EMAIL_PASSWORD=your-app-password
POP3_SERVER=pop.gmail.com
POP3_PORT=995

# LINE 通知配置 (可選)
LINE_CHANNEL_ACCESS_TOKEN=your-line-token
LINE_USER_ID=your-line-user-id
LINE_NOTIFY_PARSING_FAILURE=true
LINE_NOTIFY_PARSING_SUCCESS=false

# 系統配置
FLASK_ENV=production
USE_MEMORY_BROKER=false
EOF
        
        print_color "📝 已創建 .env 文件，請編輯後重新運行" "$GREEN"
        print_color "主要需要設定: EMAIL_ADDRESS, EMAIL_PASSWORD, POP3_SERVER" "$YELLOW"
        return 1
    fi
    return 0
}

build_container() {
    local force_rebuild="$1"
    
    print_color "🔨 開始構建 Outlook Summary 容器..." "$YELLOW"
    
    local build_args="build -t outlook-summary -f Containerfile ."
    if [ "$force_rebuild" = "true" ]; then
        build_args="build -t outlook-summary -f Containerfile . --no-cache"
        print_color "🔄 使用 --no-cache 強制重新構建..." "$YELLOW"
    fi
    
    if podman $build_args; then
        print_color "✅ 容器構建成功!" "$GREEN"
        return 0
    else
        print_color "❌ 容器構建失敗!" "$RED"
        return 1
    fi
}

start_services() {
    local in_background="$1"
    
    print_color "🚀 正在啟動 Outlook Summary 服務..." "$YELLOW"
    
    # 檢查是否有 podman-compose
    if ! command -v podman-compose &> /dev/null; then
        print_color "⚠️  podman-compose 未找到，嘗試使用 docker-compose..." "$YELLOW"
        if command -v docker-compose &> /dev/null; then
            COMPOSE_CMD="docker-compose"
        else
            print_color "❌ 錯誤: 需要安裝 podman-compose 或 docker-compose" "$RED"
            print_color "安裝方法: pip install podman-compose" "$YELLOW"
            return 1
        fi
    else
        COMPOSE_CMD="podman-compose"
    fi
    
    if [ "$in_background" = "true" ]; then
        print_color "📦 在背景模式啟動服務..." "$CYAN"
        $COMPOSE_CMD -f podman-compose.yml up -d
    else
        print_color "📦 在前台模式啟動服務..." "$CYAN"
        $COMPOSE_CMD -f podman-compose.yml up
    fi
    
    if [ $? -eq 0 ]; then
        print_color "✅ 服務啟動成功!" "$GREEN"
        print_color "🌐 訪問地址: http://localhost:5000" "$GREEN"
        print_color "📊 健康檢查: http://localhost:5000/health" "$GREEN"
        return 0
    else
        print_color "❌ 服務啟動失敗!" "$RED"
        return 1
    fi
}

stop_services() {
    print_color "🛑 正在停止 Outlook Summary 服務..." "$YELLOW"
    
    # 使用相同的 compose 命令檢測邏輯
    if command -v podman-compose &> /dev/null; then
        COMPOSE_CMD="podman-compose"
    elif command -v docker-compose &> /dev/null; then
        COMPOSE_CMD="docker-compose"
    else
        print_color "❌ 錯誤: 未找到 compose 命令" "$RED"
        return 1
    fi
    
    if $COMPOSE_CMD -f podman-compose.yml down; then
        print_color "✅ 服務已停止" "$GREEN"
        return 0
    else
        print_color "❌ 停止服務失敗!" "$RED"
        return 1
    fi
}

show_logs() {
    print_color "📋 顯示服務日誌..." "$YELLOW"
    
    if command -v podman-compose &> /dev/null; then
        podman-compose -f podman-compose.yml logs -f
    elif command -v docker-compose &> /dev/null; then
        docker-compose -f podman-compose.yml logs -f
    else
        print_color "❌ 無法找到 compose 命令" "$RED"
        return 1
    fi
}

show_status() {
    print_color "📊 檢查服務狀態..." "$YELLOW"
    
    if command -v podman-compose &> /dev/null; then
        COMPOSE_CMD="podman-compose"
    elif command -v docker-compose &> /dev/null; then
        COMPOSE_CMD="docker-compose"
    else
        print_color "❌ 無法找到 compose 命令" "$RED"
        return 1
    fi
    
    $COMPOSE_CMD -f podman-compose.yml ps
    print_color "\n🔍 容器詳細信息:" "$CYAN"
    podman ps -a --filter "name=outlook" || docker ps -a --filter "name=outlook"
}

show_help() {
    print_color "使用方法: $0 [模式] [背景模式] [強制重建]" "$CYAN"
    print_color "模式選項:" "$WHITE"
    print_color "  build     - 構建容器" "$WHITE"
    print_color "  up        - 啟動服務 (默認)" "$WHITE"
    print_color "  down      - 停止服務" "$WHITE"
    print_color "  rebuild   - 重建並啟動" "$WHITE"
    print_color "  logs      - 查看日誌" "$WHITE"
    print_color "  status    - 查看狀態" "$WHITE"
    print_color "  help      - 顯示幫助" "$WHITE"
    print_color "\n範例:" "$YELLOW"
    print_color "  $0 up true        # 背景啟動" "$WHITE"
    print_color "  $0 rebuild false true  # 強制重建並前台啟動" "$WHITE"
}

# 主執行邏輯
show_header

# 檢查幫助
if [ "$MODE" = "help" ]; then
    show_help
    exit 0
fi

# 檢查 Podman 安裝
if ! test_podman_installed; then
    exit 1
fi

# 檢查 .env 文件
if ! test_env_file; then
    exit 1
fi

# 根據模式執行操作
case "$MODE" in
    "build")
        if build_container "$FORCE"; then
            print_color "🎉 構建完成! 使用 '$0 up' 啟動服務" "$GREEN"
        else
            exit 1
        fi
        ;;
        
    "up")
        # 檢查鏡像是否存在
        if ! podman images outlook-summary --format "{{.Repository}}" | grep -q "outlook-summary" || [ "$FORCE" = "true" ]; then
            print_color "📦 鏡像不存在，開始構建..." "$YELLOW"
            if ! build_container "$FORCE"; then
                exit 1
            fi
        fi
        
        if start_services "$BACKGROUND"; then
            if [ "$BACKGROUND" = "true" ]; then
                print_color "✨ 服務已在背景運行!" "$GREEN"
                print_color "💡 使用 '$0 logs' 查看日誌" "$CYAN"
                print_color "💡 使用 '$0 status' 查看狀態" "$CYAN"
            fi
        else
            exit 1
        fi
        ;;
        
    "down")
        if ! stop_services; then
            exit 1
        fi
        ;;
        
    "rebuild")
        print_color "🔄 重建並啟動服務..." "$YELLOW"
        if build_container "true"; then
            start_services "$BACKGROUND"
        else
            exit 1
        fi
        ;;
        
    "logs")
        show_logs
        ;;
        
    "status")
        show_status
        ;;
        
    *)
        print_color "❌ 未知模式: $MODE" "$RED"
        show_help
        exit 1
        ;;
esac

print_color "\n🎯 Podman 容器化部署完成!" "$GREEN"