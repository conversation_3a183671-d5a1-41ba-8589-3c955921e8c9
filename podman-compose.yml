# Podman Compose 配置文件
# 半導體測試數據自動化郵件處理系統 - 完整部署架構
version: '3.8'

services:
  # Redis 服務 (Dramatiq 任務佇列)
  redis:
    image: docker.io/redis:7-alpine
    container_name: outlook_redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
    restart: unless-stopped
    networks:
      - outlook_network

  # 主應用服務 (Flask + FastAPI + Dramatiq)
  outlook_app:
    build:
      context: .
      dockerfile: Containerfile
    container_name: outlook_main
    ports:
      - "5000:5000"  # 統一使用 port 5000
    depends_on:
      redis:
        condition: service_healthy
    environment:
      # 應用配置
      - FLASK_PORT=5000
      - FASTAPI_PORT=5001
      - FLASK_ENV=production
      
      # Redis 配置
      - USE_MEMORY_BROKER=false
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      
      # 郵件配置 (需要從 .env 設置)
      - EMAIL_ADDRESS=${EMAIL_ADDRESS}
      - EMAIL_PASSWORD=${EMAIL_PASSWORD}
      - POP3_SERVER=${POP3_SERVER}
      - POP3_PORT=${POP3_PORT}
      
      # LINE 通知配置 (可選)
      - LINE_CHANNEL_ACCESS_TOKEN=${LINE_CHANNEL_ACCESS_TOKEN}
      - LINE_USER_ID=${LINE_USER_ID}
      - LINE_NOTIFY_PARSING_FAILURE=${LINE_NOTIFY_PARSING_FAILURE:-true}
      - LINE_NOTIFY_PARSING_SUCCESS=${LINE_NOTIFY_PARSING_SUCCESS:-false}
      
      # 容器化配置
      - PYTHONPATH=/app
      - PYTHONIOENCODING=utf-8
      - PYTHONUTF8=1
      
    volumes:
      # 持久化數據
      - app_data:/app/data
      - app_logs:/app/logs
      - app_uploads:/app/uploads
      - app_temp:/app/temp
      
      # 配置文件 (如果需要覆蓋默認配置)
      - ./.env:/app/.env:ro
      
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
      
    restart: unless-stopped
    networks:
      - outlook_network

# 網絡配置
networks:
  outlook_network:
    driver: bridge

# 數據卷配置
volumes:
  redis_data:
    driver: local
  app_data:
    driver: local
  app_logs:
    driver: local
  app_uploads:
    driver: local
  app_temp:
    driver: local