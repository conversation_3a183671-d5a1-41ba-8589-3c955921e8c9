# PowerShell 腳本 - Podman 容器化 Outlook Summary 系統啟動
# 半導體測試數據自動化郵件處理系統 - 一鍵部署腳本

param(
    [Parameter(HelpMessage="啟動模式: build (重新構建), up (啟動), down (停止), rebuild (重建並啟動), logs (查看日誌)")]
    [ValidateSet("build", "up", "down", "rebuild", "logs", "status")]
    [string]$Mode = "up",
    
    [Parameter(HelpMessage="是否在背景運行")]
    [switch]$Background = $false,
    
    [Parameter(HelpMessage="是否強制重新構建")]
    [switch]$Force = $false
)

# 顏色輸出函數
function Write-ColorOutput {
    param([string]$Message, [string]$Color = "White")
    $colors = @{
        "Red" = "Red"
        "Green" = "Green" 
        "Yellow" = "Yellow"
        "Blue" = "Blue"
        "Cyan" = "Cyan"
        "White" = "White"
    }
    Write-Host $Message -ForegroundColor $colors[$Color]
}

function Show-Header {
    Write-ColorOutput "============================================================" "Blue"
    Write-ColorOutput "🐧 Podman 容器化 - Outlook Summary 系統" "Cyan" 
    Write-ColorOutput "半導體測試數據自動化郵件處理系統" "White"
    Write-ColorOutput "============================================================" "Blue"
    Write-ColorOutput "📧 主要服務: http://localhost:5000" "Green"
    Write-ColorOutput "🔧 Dramatiq Worker: 背景運行" "Green"
    Write-ColorOutput "💾 Redis: 內建任務佇列" "Green"
    Write-ColorOutput "============================================================" "Blue"
}

function Test-PodmanInstalled {
    try {
        $version = podman --version 2>$null
        Write-ColorOutput "✅ 檢測到 Podman: $version" "Green"
        return $true
    }
    catch {
        Write-ColorOutput "❌ 錯誤: 未檢測到 Podman 安裝" "Red"
        Write-ColorOutput "請先安裝 Podman: https://podman.io/getting-started/installation" "Yellow"
        return $false
    }
}

function Test-EnvFile {
    if (-not (Test-Path ".env")) {
        Write-ColorOutput "⚠️  警告: 未找到 .env 文件" "Yellow"
        Write-ColorOutput "正在創建範例 .env 文件..." "Yellow"
        
        @"
# 郵件配置 (必要)
EMAIL_ADDRESS=<EMAIL>
EMAIL_PASSWORD=your-app-password
POP3_SERVER=pop.gmail.com
POP3_PORT=995

# LINE 通知配置 (可選)
LINE_CHANNEL_ACCESS_TOKEN=your-line-token
LINE_USER_ID=your-line-user-id
LINE_NOTIFY_PARSING_FAILURE=true
LINE_NOTIFY_PARSING_SUCCESS=false

# 系統配置
FLASK_ENV=production
USE_MEMORY_BROKER=false
"@ | Out-File -FilePath ".env" -Encoding UTF8
        
        Write-ColorOutput "📝 已創建 .env 文件，請編輯後重新運行" "Green"
        Write-ColorOutput "主要需要設定: EMAIL_ADDRESS, EMAIL_PASSWORD, POP3_SERVER" "Yellow"
        return $false
    }
    return $true
}

function Build-Container {
    param([bool]$ForceRebuild = $false)
    
    Write-ColorOutput "🔨 開始構建 Outlook Summary 容器..." "Yellow"
    
    $buildArgs = @("build", "-t", "outlook-summary", "-f", "Containerfile", ".")
    if ($ForceRebuild) {
        $buildArgs += "--no-cache"
        Write-ColorOutput "🔄 使用 --no-cache 強制重新構建..." "Yellow"
    }
    
    try {
        & podman @buildArgs
        if ($LASTEXITCODE -eq 0) {
            Write-ColorOutput "✅ 容器構建成功!" "Green"
            return $true
        }
        else {
            Write-ColorOutput "❌ 容器構建失敗!" "Red"
            return $false
        }
    }
    catch {
        Write-ColorOutput "❌ 構建過程中發生錯誤: $($_.Exception.Message)" "Red"
        return $false
    }
}

function Start-Services {
    param([bool]$InBackground = $false)
    
    Write-ColorOutput "🚀 正在啟動 Outlook Summary 服務..." "Yellow"
    
    try {
        if ($InBackground) {
            Write-ColorOutput "📦 在背景模式啟動服務..." "Cyan"
            & podman-compose -f podman-compose.yml up -d
        }
        else {
            Write-ColorOutput "📦 在前台模式啟動服務..." "Cyan"
            & podman-compose -f podman-compose.yml up
        }
        
        if ($LASTEXITCODE -eq 0) {
            Write-ColorOutput "✅ 服務啟動成功!" "Green"
            Write-ColorOutput "🌐 訪問地址: http://localhost:5000" "Green"
            Write-ColorOutput "📊 健康檢查: http://localhost:5000/health" "Green"
            return $true
        }
        else {
            Write-ColorOutput "❌ 服務啟動失敗!" "Red"
            return $false
        }
    }
    catch {
        Write-ColorOutput "❌ 啟動過程中發生錯誤: $($_.Exception.Message)" "Red"
        return $false
    }
}

function Stop-Services {
    Write-ColorOutput "🛑 正在停止 Outlook Summary 服務..." "Yellow"
    
    try {
        & podman-compose -f podman-compose.yml down
        if ($LASTEXITCODE -eq 0) {
            Write-ColorOutput "✅ 服務已停止" "Green"
            return $true
        }
        else {
            Write-ColorOutput "❌ 停止服務失敗!" "Red"
            return $false
        }
    }
    catch {
        Write-ColorOutput "❌ 停止過程中發生錯誤: $($_.Exception.Message)" "Red"
        return $false
    }
}

function Show-Logs {
    Write-ColorOutput "📋 顯示服務日誌..." "Yellow"
    try {
        & podman-compose -f podman-compose.yml logs -f
    }
    catch {
        Write-ColorOutput "❌ 無法查看日誌: $($_.Exception.Message)" "Red"
    }
}

function Show-Status {
    Write-ColorOutput "📊 檢查服務狀態..." "Yellow"
    try {
        & podman-compose -f podman-compose.yml ps
        Write-ColorOutput "`n🔍 容器詳細信息:" "Cyan"
        & podman ps -a --filter "name=outlook"
    }
    catch {
        Write-ColorOutput "❌ 無法查看狀態: $($_.Exception.Message)" "Red"
    }
}

# 主執行邏輯
Show-Header

# 檢查 Podman 安裝
if (-not (Test-PodmanInstalled)) {
    exit 1
}

# 檢查 .env 文件
if (-not (Test-EnvFile)) {
    exit 1
}

# 根據模式執行操作
switch ($Mode) {
    "build" {
        if (Build-Container -ForceRebuild $Force) {
            Write-ColorOutput "🎉 構建完成! 使用 'start_podman.ps1 -Mode up' 啟動服務" "Green"
        }
        else {
            exit 1
        }
    }
    
    "up" {
        # 先嘗試構建（如果鏡像不存在）
        $imageExists = (podman images outlook-summary --format "{{.Repository}}" 2>$null) -ne ""
        if (-not $imageExists -or $Force) {
            Write-ColorOutput "📦 鏡像不存在，開始構建..." "Yellow"
            if (-not (Build-Container -ForceRebuild $Force)) {
                exit 1
            }
        }
        
        if (Start-Services -InBackground $Background) {
            if ($Background) {
                Write-ColorOutput "✨ 服務已在背景運行!" "Green"
                Write-ColorOutput "💡 使用 'start_podman.ps1 -Mode logs' 查看日誌" "Cyan"
                Write-ColorOutput "💡 使用 'start_podman.ps1 -Mode status' 查看狀態" "Cyan"
            }
        }
        else {
            exit 1
        }
    }
    
    "down" {
        if (-not (Stop-Services)) {
            exit 1
        }
    }
    
    "rebuild" {
        Write-ColorOutput "🔄 重建並啟動服務..." "Yellow"
        if (Build-Container -ForceRebuild $true) {
            Start-Services -InBackground $Background
        }
        else {
            exit 1
        }
    }
    
    "logs" {
        Show-Logs
    }
    
    "status" {
        Show-Status
    }
}

Write-ColorOutput "`n🎯 Podman 容器化部署完成!" "Green"