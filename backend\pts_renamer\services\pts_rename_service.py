"""
PTS Renamer Core Service

This module contains the main service orchestrator for PTS file processing.
It coordinates between different processing services and manages the overall
workflow according to MVP architecture principles.
"""

from typing import List, Dict, Any, Optional, Tuple
from pathlib import Path
from datetime import datetime
import asyncio
# import logging  # Using UnifiedLogger instead

from ..models.pts_rename_entities import (
    PTSProcessingJob,
    PTSFile,
    PTSRenameResult,
    PTSOperationType,
    PTSQCFile,
    PTSDirectory,
    JobId,
    UploadId
)
from ..models.pts_rename_models import (
    PTSRenameJobRequest,
    PTSRenameJobStatus,
    PTSRenamePreviewResponse,
    PTSFilePreview
)
from ..repositories.pts_rename_repository import IPTSRenameRepository
from .pts_rename_task_queue import PTSRenameTaskQueue
# Using loguru for logging (already imported in other services)


class PTSRenameService:
    """
    Core service for PTS file processing
    
    This service orchestrates the entire PTS processing workflow:
    - Coordinates file processing, QC generation, and directory creation
    - Manages batch operations and result finalization
    - Integrates with existing Dramatiq infrastructure
    - Handles error recovery and cleanup
    """
    
    def __init__(self,
                 repository: IPTSRenameRepository,
                 processor: 'PTSRenameProcessor',
                 qc_generator: 'PTSQCGenerator',
                 directory_manager: 'PTSDirectoryManager',
                 download_service: 'PTSRenameDownloadService',
                 task_queue: PTSRenameTaskQueue,
                 logger = None):
        """
        Initialize PTS Rename Service
        
        Args:
            repository: Data access repository
            processor: File renaming processor
            qc_generator: QC file generator
            directory_manager: Directory management service
            download_service: Download and compression service
            task_queue: PTS-specific task queue for async processing
            logger: Optional logger instance
        """
        self.repository = repository
        self.processor = processor
        self.qc_generator = qc_generator
        self.directory_manager = directory_manager
        self.download_service = download_service
        self.task_queue = task_queue
        # Using loguru logger (imported in other services)
        from loguru import logger as loguru_logger
        self.logger = logger or loguru_logger
    
    async def process_pts_files(self, job_request: PTSRenameJobRequest) -> Dict[str, Any]:
        """
        Process PTS files directly according to request
        
        This method orchestrates the entire processing workflow directly:
        1. Create processing job
        2. Process files directly (no Dramatiq queuing)
        3. Generate detailed processing logs
        4. Return comprehensive results with path information
        
        Args:
            job_request: Processing request with configuration
        
    Returns:
        Dictionary containing processing results and detailed logs
        
    Raises:
        ServiceError: If processing setup fails
        """
        try:
            import time
            start_time = time.time()
            
            # Log processing request details
            self.logger.info(f"[PROCESS] Starting direct PTS file processing for upload: {job_request.upload_id}")
            self.logger.info(f"[PROCESS] Requested operations: {[str(op) for op in job_request.operations]}")
            
            # Get PTS files for the upload
            pts_files = await self.repository.get_pts_files(job_request.upload_id)
            if not pts_files:
                error_details = {
                    "upload_id": job_request.upload_id,
                    "operations": [str(op) for op in job_request.operations],
                    "message": "No PTS files were found in the upload directory",
                    "suggestion": "Please verify that the upload contains .pts files and was extracted properly"
                }
                self.logger.error(f"[PROCESS] No PTS files found for upload {job_request.upload_id}")
                raise ServiceError(
                    f"No PTS files found for upload {job_request.upload_id}",
                    details=error_details
                )
            
            # Log found files for debugging
            self.logger.info(f"[PROCESS] Found {len(pts_files)} PTS files:")
            for pts_file in pts_files:
                self.logger.info(f"[PROCESS] - {pts_file.filename} ({pts_file.size} bytes) at: {pts_file.original_path}")
            
            # Convert rename_config to RenamePattern if provided
            rename_pattern = None
            if job_request.rename_config:
                from ..models.pts_rename_entities import RenamePattern
                rename_pattern = RenamePattern(
                    old_pattern=job_request.rename_config.get('old_pattern', ''),
                    new_pattern=job_request.rename_config.get('new_pattern', '')
                )
                self.logger.info(f"[PROCESS] Rename pattern: {rename_pattern.old_pattern} -> {rename_pattern.new_pattern}")
            
            # Create processing job
            job = PTSProcessingJob(
                job_id=JobId(self._generate_job_id()),
                upload_id=UploadId(job_request.upload_id),
                pts_files=pts_files,
                operations=[PTSOperationType(op) for op in job_request.operations],
                rename_pattern=rename_pattern,
                qc_enabled=job_request.qc_enabled,
                create_directories=job_request.create_directories
            )
            
            # Save job to repository
            self.logger.info(f"[PROCESS] Saving job with {len(job.pts_files)} PTS files")
            job_id = await self.repository.save_job(job)
            self.logger.info(f"[PROCESS] Job {job_id} saved successfully")
            
            # Start processing
            job.start_processing()
            await self.repository.save_job(job)
            
            # Process files directly
            processing_logs = []
            results = []
            total_files = len(pts_files)
            
            for i, pts_file in enumerate(pts_files):
                try:
                    file_start_time = time.time()
                    self.logger.info(f"[PROCESS] Processing file {i+1}/{total_files}: {pts_file.filename}")
                    
                    # Process single file and get detailed logs
                    file_result = await self._process_single_pts_file_direct(
                        pts_file, job, processing_logs
                    )
                    results.append(file_result)
                    
                    # Log processing time for this file
                    file_processing_time = time.time() - file_start_time
                    processing_logs.append({
                        "timestamp": datetime.now().isoformat(),
                        "level": "INFO",
                        "message": f"File {pts_file.filename} processed in {file_processing_time:.2f}s"
                    })
                    
                except Exception as e:
                    error_msg = f"Failed to process file {pts_file.filename}: {e}"
                    self.logger.error(f"[PROCESS] {error_msg}")
                    processing_logs.append({
                        "timestamp": datetime.now().isoformat(),
                        "level": "ERROR",
                        "message": error_msg
                    })
                    file_result = {
                        'filename': pts_file.filename,
                        'original_path': pts_file.original_path,
                        'status': 'failed',
                        'error': str(e)
                    }
                    results.append(file_result)
            
            # Create download package
            processing_logs.append({
                "timestamp": datetime.now().isoformat(),
                "level": "INFO",
                "message": "Creating download package..."
            })
            
            processed_files = [r.get('output_path') for r in results if r.get('output_path')]
            download_url = None
            if processed_files:
                try:
                    download_url = await self.download_service.create_download_package(
                        job_id, processed_files
                    )
                    processing_logs.append({
                        "timestamp": datetime.now().isoformat(),
                        "level": "INFO",
                        "message": f"Download package created: {download_url}"
                    })
                except Exception as e:
                    error_msg = f"Failed to create download package: {e}"
                    self.logger.error(f"[PROCESS] {error_msg}")
                    processing_logs.append({
                        "timestamp": datetime.now().isoformat(),
                        "level": "ERROR",
                        "message": error_msg
                    })
            
            # Finalize job
            processing_time = time.time() - start_time
            job.mark_as_completed(download_url=download_url)
            await self.repository.save_job(job)
            
            successful_files = len([r for r in results if r.get('status') == 'success'])
            failed_files = len([r for r in results if r.get('status') == 'failed'])
            
            processing_logs.append({
                "timestamp": datetime.now().isoformat(),
                "level": "INFO",
                "message": f"Processing completed: {successful_files} successful, {failed_files} failed, total time: {processing_time:.2f}s"
            })
            
            self.logger.info(f"[PROCESS] Direct processing completed for job {job_id}")
            
            # Return comprehensive results
            return {
                'status': 'completed',
                'success': True,
                'job_id': job_id,
                'total_files': total_files,
                'processed_files': successful_files,
                'failed_files': failed_files,
                'results': results,
                'download_url': download_url,
                'processing_time': processing_time,
                'processing_logs': processing_logs,
                'completed_at': datetime.now().isoformat()
            }
            
        except Exception as e:
            import traceback
            self.logger.error(f"Failed to process PTS files: {e}")
            self.logger.error(f"Full traceback: {traceback.format_exc()}")
            raise ServiceError(f"Processing setup failed: {e}")
    
    async def generate_preview(self, upload_id: str, operations: List[str], 
                             rename_config: Optional[Dict[str, str]] = None) -> PTSRenamePreviewResponse:
        """
        Generate processing preview
        
        Args:
            upload_id: Upload identifier
            operations: List of operations to preview
            rename_config: Rename configuration if applicable
            
        Returns:
            Preview response with file-by-file preview
        """
        try:
            # DEBUG: Add to errors so we can see it in the response
            errors = []
            errors.append(f"DEBUG: generate_preview called")
            errors.append(f"DEBUG: upload_id={upload_id}")
            errors.append(f"DEBUG: operations={operations}")
            errors.append(f"DEBUG: rename_config={rename_config}")
            
            # Get PTS files
            pts_files = await self.repository.get_pts_files(upload_id)
            errors.append(f"DEBUG: Retrieved {len(pts_files) if pts_files else 0} PTS files")
            
            if not pts_files:
                errors.append(f"DEBUG: No PTS files found - returning early")
                return PTSRenamePreviewResponse(
                    upload_id=upload_id,
                    total_files=0,
                    files_preview=[],
                    operations_summary={},
                    estimated_processing_time=0,
                    errors=["No PTS files found for upload"] + errors
                )
            
            # DEBUG: Log first few files
            for i, pts_file in enumerate(pts_files[:3]):
                errors.append(f"DEBUG: File {i+1}: {pts_file.filename} at {pts_file.original_path}")
            
            # Generate preview for each file
            files_preview = []
            warnings = []
            
            errors.append(f"DEBUG: Starting file preview generation for {len(pts_files)} files")
            
            for i, pts_file in enumerate(pts_files):
                try:
                    errors.append(f"DEBUG: Processing file {i+1}: {pts_file.filename}")
                    preview = await self._generate_file_preview(
                        pts_file, operations, rename_config
                    )
                    files_preview.append(preview)
                    warnings.extend(preview.warnings)
                    errors.append(f"DEBUG: File {i+1} processed successfully, warnings: {len(preview.warnings)}")
                except Exception as e:
                    error_msg = f"Preview failed for {pts_file.filename}: {e}"
                    errors.append(error_msg)
                    errors.append(f"DEBUG: Exception details: {str(e)}")
            
            errors.append(f"DEBUG: File preview generation completed, {len(files_preview)} previews created")
            
            # Calculate operations summary
            operations_summary = self._calculate_operations_summary(files_preview)
            errors.append(f"DEBUG: Operations summary: {operations_summary}")
            
            # Estimate processing time (rough calculation)
            estimated_time = self._estimate_processing_time(len(pts_files), operations)
            
            return PTSRenamePreviewResponse(
                upload_id=upload_id,
                total_files=len(pts_files),
                files_preview=files_preview,
                operations_summary=operations_summary,
                estimated_processing_time=estimated_time,
                warnings=list(set(warnings)),  # Remove duplicates
                errors=errors
            )
            
        except Exception as e:
            error_msg = f"Preview generation failed: {e}"
            self.logger.error(error_msg)
            return PTSRenamePreviewResponse(
                upload_id=upload_id,
                total_files=0,
                files_preview=[],
                operations_summary={},
                estimated_processing_time=0,
                errors=[error_msg, f"DEBUG: Exception in generate_preview: {str(e)}"]
            )
    
    async def finalize_processing(self, job_id: str, processed_files: List[str]) -> str:
        """
        Finalize processing by compressing results and creating download URL
        
        Args:
            job_id: Job identifier
            processed_files: List of processed file paths
            
        Returns:
            Download URL for frontend
        """
        try:
            # Get job details
            job = await self.repository.get_job(job_id)
            if not job:
                raise ServiceError(f"Job {job_id} not found")
            
            # Create download package using existing compression infrastructure
            download_url = await self.download_service.create_download_package(
                job_id, processed_files
            )
            
            # Update job with download URL
            job.mark_as_completed(download_url=download_url)
            await self.repository.save_job(job)
            
            self.logger.info(f"Finalized processing for job {job_id}, download URL: {download_url}")
            return download_url
            
        except Exception as e:
            self.logger.error(f"Failed to finalize processing: {e}")
            raise ServiceError(f"Finalization failed: {e}")
    
    async def get_job_status(self, job_id: str) -> Optional[PTSRenameJobStatus]:
        """
        Get current job status
        
        Args:
            job_id: Job identifier
            
        Returns:
            Job status or None if not found
        """
        try:
            job = await self.repository.get_job(job_id)
            if not job:
                return None
            
            return PTSRenameJobStatus(
                job_id=str(job.job_id),
                status=job.status,
                progress=job.progress_percentage,
                files_processed=job.files_processed,
                total_files=job.total_files,
                error_message=job.error_message,
                result_download_url=job.download_url,
                download_expires_at=job.download_expires_at,
                created_at=job.created_at or datetime.now(),
                updated_at=job.updated_at or datetime.now()
            )
            
        except Exception as e:
            self.logger.error(f"Failed to get job status: {e}")
            return None
    
    async def cleanup_expired_jobs(self, retention_hours: int = 24) -> int:
        """
        Clean up expired jobs and associated files
        
        Args:
            retention_hours: Hours after which jobs are considered expired
            
        Returns:
            Number of jobs cleaned up
        """
        try:
            deleted_count = await self.repository.delete_expired_jobs(retention_hours)
            self.logger.info(f"Cleaned up {deleted_count} expired PTS processing jobs")
            return deleted_count
            
        except Exception as e:
            self.logger.error(f"Cleanup failed: {e}")
            return 0
    
    # Private helper methods
    
    def _generate_job_id(self) -> str:
        """Generate unique job ID"""
        import uuid
        return f"pts_job_{uuid.uuid4().hex[:12]}"
    
    def _serialize_job_for_queue(self, job: PTSProcessingJob) -> Dict[str, Any]:
        """Serialize job for task queue"""
        return {
            "job_id": job.job_id.value if hasattr(job.job_id, 'value') else str(job.job_id),
            "upload_id": job.upload_id.value if hasattr(job.upload_id, 'value') else str(job.upload_id),
            "operations": [op.value if hasattr(op, 'value') else str(op) for op in job.operations],
            "rename_config": {
                "old_pattern": job.rename_pattern.old_pattern if job.rename_pattern else "",
                "new_pattern": job.rename_pattern.new_pattern if job.rename_pattern else ""
            } if job.rename_pattern else None,
            "qc_enabled": job.qc_enabled,
            "create_directories": job.create_directories,
            "total_files": job.total_files
        }
    
    async def _generate_file_preview(self, pts_file: PTSFile, operations: List[str], 
                                   rename_config: Optional[Dict[str, str]]) -> PTSFilePreview:
        """Generate preview for a single file"""
        warnings = []
        operations_applied = []
        
        # DEBUG: Add debug info to warnings so we can see it in the API response
        warnings.append(f"DEBUG: filename={pts_file.filename}")
        warnings.append(f"DEBUG: operations={operations}")
        warnings.append(f"DEBUG: rename_config={rename_config}")
        
        # Preview rename operation
        new_name = None
        # Check for both string and enum representations
        rename_in_operations = ("rename" in operations or 
                              "PTSRenameOperation.RENAME" in [str(op) for op in operations] or
                              any(str(op).endswith("RENAME") for op in operations))
        has_rename_config = rename_config is not None
        warnings.append(f"DEBUG: rename_in_operations={rename_in_operations}")
        warnings.append(f"DEBUG: has_rename_config={has_rename_config}")
        
        if rename_in_operations and rename_config:
            try:
                # Create a Path object for the processor
                file_path = Path(pts_file.original_path)
                old_pattern = rename_config.get("old_pattern", "")
                new_pattern = rename_config.get("new_pattern", "")
                
                warnings.append(f"DEBUG: file_path={file_path}")
                warnings.append(f"DEBUG: old_pattern='{old_pattern}'")
                warnings.append(f"DEBUG: new_pattern='{new_pattern}'")
                
                new_name = self.processor.generate_new_name(
                    file_path, 
                    old_pattern,
                    new_pattern
                )
                
                warnings.append(f"DEBUG: generated_new_name='{new_name}'")
                warnings.append(f"DEBUG: comparison: '{new_name}' != '{pts_file.filename}' = {new_name != pts_file.filename}")
                
                if new_name != pts_file.filename:
                    operations_applied.append("rename")
                    warnings.append(f"DEBUG: Added 'rename' to operations_applied")
                else:
                    warning_msg = f"Rename pattern does not match {pts_file.filename}"
                    warnings.append(warning_msg)
                    warnings.append(f"DEBUG: Added warning: {warning_msg}")
                    
            except Exception as e:
                warning_msg = f"Rename preview failed: {e}"
                warnings.append(warning_msg)
                warnings.append(f"DEBUG: Exception occurred: {e}")
        else:
            warnings.append(f"DEBUG: Skipping rename logic because condition not met")
        
        # Preview QC generation
        qc_file_name = None
        qc_in_operations = ("qc_generation" in operations or 
                          any(str(op).endswith("QC_GENERATION") for op in operations))
        if qc_in_operations:
            file_path = Path(pts_file.filename)
            qc_file_name = f"{file_path.stem}_QC{file_path.suffix}"
            operations_applied.append("qc_generation")
        
        # Preview directory creation
        directory_name = None
        dir_in_operations = ("directory_creation" in operations or 
                           any(str(op).endswith("DIRECTORY_CREATION") for op in operations))
        if dir_in_operations:
            directory_name = Path(pts_file.filename).stem
            operations_applied.append("directory_creation")
        
        warnings.append(f"DEBUG: Final new_name={new_name}")
        warnings.append(f"DEBUG: Final operations_applied={operations_applied}")
        
        return PTSFilePreview(
            original_name=pts_file.filename,
            new_name=new_name,
            qc_file_name=qc_file_name,
            directory_name=directory_name,
            file_size=pts_file.size,
            operations_applied=operations_applied,
            warnings=warnings
        )
    
    def _calculate_operations_summary(self, files_preview: List[PTSFilePreview]) -> Dict[str, int]:
        """Calculate summary of operations to be performed"""
        summary = {
            "rename": 0,
            "qc_generation": 0,
            "directory_creation": 0
        }
        
        for preview in files_preview:
            for operation in preview.operations_applied:
                if operation in summary:
                    summary[operation] += 1
        
        return summary
    
    def _estimate_processing_time(self, file_count: int, operations: List[str]) -> int:
        """Estimate processing time in seconds"""
        # Base time per file
        base_time = 2  # seconds per file
        
        # Additional time per operation
        operation_time = {
            "rename": 1,
            "qc_generation": 3,
            "directory_creation": 5
        }
        
        total_time = file_count * base_time
        for operation in operations:
            if operation in operation_time:
                total_time += file_count * operation_time[operation]
        
        # Add compression time
        total_time += min(30, file_count * 0.5)
        
        return int(total_time)
    
    async def _process_single_pts_file_direct(self, pts_file: PTSFile, job: PTSProcessingJob, 
                                            processing_logs: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Process a single PTS file directly according to job configuration
        
        Args:
            pts_file: PTS file to process
            job: Processing job configuration
            processing_logs: List to append processing logs to
            
        Returns:
            Dictionary containing processing result with detailed path information
        """
        try:
            import hashlib
            from pathlib import Path
            
            # Generate unique file ID from filename
            file_id = hashlib.md5(pts_file.filename.encode()).hexdigest()[:12]
            
            processing_logs.append({
                "timestamp": datetime.now().isoformat(),
                "level": "INFO",
                "message": f"Starting processing for file: {pts_file.filename} (ID: {file_id})"
            })
            
            result = {
                'file_id': file_id,
                'filename': pts_file.filename,
                'original_path': pts_file.original_path,
                'operations_performed': [],
                'output_files': [],
                'status': 'success',
                'path_changes': []  # Track path changes for detailed logging
            }
            
            current_file_path = Path(pts_file.original_path)
            processing_logs.append({
                "timestamp": datetime.now().isoformat(),
                "level": "INFO",
                "message": f"Initial file path: {current_file_path}"
            })
            
            # Process operations in order
            for operation in job.operations:
                try:
                    if operation == PTSOperationType.RENAME:
                        # Rename file using rename_pattern
                        if job.rename_pattern:
                            processing_logs.append({
                                "timestamp": datetime.now().isoformat(),
                                "level": "INFO",
                                "message": f"Applying rename pattern: '{job.rename_pattern.old_pattern}' -> '{job.rename_pattern.new_pattern}'"
                            })
                            
                            success, old_name, new_name_or_error = self.processor.rename_file(
                                current_file_path,
                                job.rename_pattern.old_pattern,
                                job.rename_pattern.new_pattern
                            )
                            if success:
                                # File was already renamed by the service
                                new_file_path = current_file_path.parent / new_name_or_error
                                result['path_changes'].append({
                                    'operation': 'rename',
                                    'from': str(current_file_path),
                                    'to': str(new_file_path)
                                })
                                current_file_path = new_file_path
                                result['operations_performed'].append('rename')
                                processing_logs.append({
                                    "timestamp": datetime.now().isoformat(),
                                    "level": "INFO",
                                    "message": f"Renamed: {old_name} -> {new_name_or_error}"
                                })
                                processing_logs.append({
                                    "timestamp": datetime.now().isoformat(),
                                    "level": "INFO",
                                    "message": f"New file path: {current_file_path}"
                                })
                            else:
                                processing_logs.append({
                                    "timestamp": datetime.now().isoformat(),
                                    "level": "WARNING",
                                    "message": f"Rename failed: {new_name_or_error}"
                                })
                    
                    elif operation == PTSOperationType.QC_GENERATION:
                        # Generate QC file
                        processing_logs.append({
                            "timestamp": datetime.now().isoformat(),
                            "level": "INFO",
                            "message": f"Generating QC file for: {current_file_path.name}"
                        })
                        
                        success, qc_filename_or_error = self.qc_generator.create_qc_file(
                            current_file_path
                        )
                        if success:
                            qc_file_path = current_file_path.parent / qc_filename_or_error
                            result['output_files'].append(str(qc_file_path))
                            result['operations_performed'].append('qc_generation')
                            processing_logs.append({
                                "timestamp": datetime.now().isoformat(),
                                "level": "INFO",
                                "message": f"Generated QC file: {qc_file_path}"
                            })
                        else:
                            processing_logs.append({
                                "timestamp": datetime.now().isoformat(),
                                "level": "WARNING",
                                "message": f"QC generation failed: {qc_filename_or_error}"
                            })
                    
                    elif operation == PTSOperationType.DIRECTORY_CREATION:
                        # Create directory structure
                        processing_logs.append({
                            "timestamp": datetime.now().isoformat(),
                            "level": "INFO",
                            "message": f"Creating directory structure for: {current_file_path.name}"
                        })
                        
                        original_folder = current_file_path.parent
                        success, dir_name_or_error = self.directory_manager.create_pts_directory(
                            current_file_path, original_folder
                        )
                        if success:
                            dir_path = original_folder.parent / dir_name_or_error
                            result['output_files'].append(str(dir_path))
                            result['operations_performed'].append('directory_creation')
                            processing_logs.append({
                                "timestamp": datetime.now().isoformat(),
                                "level": "INFO",
                                "message": f"Created directory: {dir_path}"
                            })
                        else:
                            processing_logs.append({
                                "timestamp": datetime.now().isoformat(),
                                "level": "WARNING",
                                "message": f"Directory creation failed: {dir_name_or_error}"
                            })
                
                except Exception as e:
                    op_name = operation.value if hasattr(operation, 'value') else str(operation)
                    error_msg = f"Operation {op_name} failed: {e}"
                    self.logger.error(f"[PROCESS] {error_msg}")
                    processing_logs.append({
                        "timestamp": datetime.now().isoformat(),
                        "level": "ERROR",
                        "message": error_msg
                    })
                    result['operations_performed'].append(f'{op_name}_failed')
            
            # Set final output path - ensure it points to the actual processed files
            result['output_path'] = str(current_file_path.parent)
            result['final_file_path'] = str(current_file_path)
            
            processing_logs.append({
                "timestamp": datetime.now().isoformat(),
                "level": "INFO",
                "message": f"File processing completed successfully. Final path: {current_file_path}"
            })
            
            return result
            
        except Exception as e:
            import hashlib
            error_msg = f"Failed to process file {pts_file.filename}: {e}"
            self.logger.error(f"[PROCESS] {error_msg}")
            processing_logs.append({
                "timestamp": datetime.now().isoformat(),
                "level": "ERROR",
                "message": error_msg
            })
            return {
                'file_id': hashlib.md5(pts_file.filename.encode()).hexdigest()[:12],
                'filename': pts_file.filename,
                'original_path': pts_file.original_path,
                'status': 'failed',
                'error': str(e)
            }


class ServiceError(Exception):
    """Exception raised by service operations"""
    
    def __init__(self, message: str, details: Dict[str, Any] = None):
        super().__init__(message)
        self.details = details or {}
        self.timestamp = datetime.now()