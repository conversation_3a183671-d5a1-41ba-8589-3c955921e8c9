# PTS Renamer 直接執行模式任務需求 - 2025-08-23

## 🎯 任務目標
將 PTS Renamer 從 Dramatiq Worker 模式改為直接執行模式，並進行完整功能驗證。

## 📋 具體需求

### **核心功能修改**
1. **移除 Dramatiq 依賴**：不再使用背景任務，直接在主程序執行
2. **保留核心邏輯**：PTSRenameProcessor 和 PTSQCGenerator 功能不變
3. **即時日誌顯示**：顯示處理過程中的詳細路徑資訊
4. **最小變動原則**：盡可能少的檔案修改，刪除不需要的 Dramatiq 相關代碼

### **預期行為**
- 按下「開始處理」後立即執行重命名和 QC 生成
- 即時顯示處理日誌，包含：
  - 更名前路徑
  - 更名後路徑
  - 成功/失敗狀態確認

## 🧪 驗證測試規格

### **測試環境**
- **測試網址**: http://localhost:5000/pts-renamer/
- **測試檔案**: `doc/GMT_G2514XX_CTAF4_F1_XX.7z`
- **虛擬環境**: venv_win_3_11_9

### **測試場景**
1. **檔案上傳測試**
   - 上傳 `GMT_G2514XX_CTAF4_F1_XX.7z` 檔案
   - 確認檔案解壓縮成功
   - 確認 PTS 檔案被正確識別

2. **重命名功能測試**
   - **替換前模式**: `CTAF4_F1_02ENG01`
   - **替換後模式**: `CTAF4_F1_02`
   - 確認模式匹配和替換邏輯正確

3. **QC 生成測試**
   - 啟用 QC 檔案生成選項
   - 確認 QC 檔案正確生成（檔名_QC.pts）

4. **日誌驗證**
   - 確認即時日誌顯示
   - 驗證路徑資訊完整性
   - 確認成功/失敗狀態清晰

### **預期結果**
- 檔案成功重命名（從包含 ENG01 的格式改為不含 ENG01）
- QC 檔案成功生成
- 日誌清楚顯示每個操作的詳細資訊
- 整個過程無需等待背景任務，立即完成

## 🛠 技術要求

### **需要修改的檔案**
1. `backend/pts_renamer/services/pts_rename_service.py` - 修改 process_pts_files 方法
2. `backend/pts_renamer/services/pts_rename_presenter.py` - 修改 handle_processing_request 方法  
3. `frontend/pts_renamer/static/js/pts_renamer.js` - 修改 startProcessing 方法
4. 刪除 `backend/pts_renamer/services/pts_rename_dramatiq_integration.py`

### **關鍵修改點**
- 將異步任務排隊改為直接同步執行
- 移除 Dramatiq 相關 import 和依賴
- 修改前端不再輪詢狀態，直接顯示結果
- 強化日誌輸出，包含詳細路徑資訊

## 📊 成功標準
1. ✅ 功能正常：重命名和 QC 生成功能完全正常
2. ✅ 即時執行：按下按鈕後立即開始處理，無延遲
3. ✅ 日誌完整：詳細顯示每個檔案的處理路徑
4. ✅ 用戶體驗：操作流程簡潔，反饋即時
5. ✅ 代碼品質：移除冗餘代碼，保持結構清晰

## 🔧 代理分工計劃
1. **Backend Developer Agent**: 負責後端服務邏輯修改
2. **Frontend Developer Agent**: 負責前端介面和 JavaScript 修改
3. **QA Engineer Agent**: 使用 Playwright 進行完整功能測試
4. **System Integration Agent**: 確保各組件整合正常運行